---
id: UNF-413-TASK-13
UNF: UNF-413
title: UNF-413-TASK-13 - Creazione Apex dc_MetadataService
type: feature
priority: high
component: disambiguation-interface
status: TODO
depends_on: []
blocked_by: []
owner: jean.sanchez
effort: 3
estimated_time: 3h
tags: [salesforce, apex]
---

# dc_MetadataService

## Descrizione
Creare la classe Apex `dc_MetadataService.cls` che carica dinamicamente i domini (professioni, occupazioni, età animale, tipologie veicolo/abitazione, paesi) con caching integrata. Espone il metodo `getDomain(String domainCode)` che restituisce una mappa `Map<String, List<DomainValue>>`.

## Motivazione
Fornisce un'interfaccia unificata per recuperare e memorizzare in cache i valori di dominio utilizzati nei vari form.

## Criteri di Accettazione
- [ ] Caricamento dei domini tramite web service e gestione cache.
- [ ] Interfaccia `getDomain` disponibile per i componenti.
- [ ] Documentazione dei domini supportati.

## Structured Chain-of-Thought
1. Definire struttura della classe e oggetto `DomainValue`.
2. Implementare caching con Custom Metadata o Custom Setting.
3. Esporre metodo `getDomain` con parametri dinamici.
