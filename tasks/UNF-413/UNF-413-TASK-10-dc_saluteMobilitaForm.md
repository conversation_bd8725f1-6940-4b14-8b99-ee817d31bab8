---
id: UNF-413-TASK-10
UNF: UNF-413
title: UNF-413-TASK-10 - Implementazione componenti dc_saluteForm e dc_mobilitaForm
type: feature
priority: high
component: disambiguation-interface
status: TODO
depends_on: [UNF-413-TASK-4]
blocked_by: []
owner: jean.sanchez
effort: 4
estimated_time: 4h
tags: [salesforce, lwc, pus, pumobilita]
---

# dc_saluteForm e dc_mobilitaForm

## Descrizione
Creare i form dedicati agli ambiti `PUSALUTE` e `PUMOBILITA` con campi: data di nascita, data effetto, professione, occupazione (con dipendenza), residenza e status familiare.

## Motivazione
Questi prodotti richiedono dati anagrafici completi per generare correttamente le offerte e verificare le coperture.

## Criteri di Accettazione
- [ ] Dipendenza professione → occupazione tramite `MetadataService`.
- [ ] Data effetto e nascita gestite con `dc_datePicker`.
- [ ] Residenza tramite `dc_addressSearch`.
- [ ] Output `referencesToUpdate` per `PUSALUTE` e `PUMOBILITA`.

## Structured Chain-of-Thought
1. Definire struttura form con tutti i campi richiesti.
2. Collegare i domini delle picklist tramite servizio metadata.
3. Serializzare i dati raccolti e restituirli nel formato richiesto.
