---
id: UNF-413-TASK-11
UNF: UNF-413
title: UNF-413-TASK-11 - Creazione Apex dc_DisambiguationService
type: feature
priority: high
component: disambiguation-interface
status: TODO
depends_on: [UNF-413-TASK-5, UNF-413-TASK-6, UNF-413-TASK-7, UNF-413-TASK-8, UNF-413-TASK-9, UNF-413-TASK-10]
blocked_by: []
owner: jean.sanchez
effort: 3
estimated_time: 3h
tags: [salesforce, apex]
---

# dc_DisambiguationService

## Descrizione
Implementare la classe Apex `dc_DisambiguationService.cls` con metodo `invokeInterpreter(Map<String, Object> payload)` in grado di costruire il payload per il componente `<c-interprete>` includendo `productType`, `action`, `env`, `referencesToUpdate` e `captchaToken` (opzionale).

## Motivazione
Serve da strato di orchestrazione tra LWC e servizi backend per invocare l'interprete con i dati raccolti.

## Criteri di Accettazione
- [ ] Metodo pubblico `invokeInterpreter` disponibile e documentato.
- [ ] Mapping corretto dei parametri nel payload.
- [ ] Gestione opzionale del `captchaToken`.

## Structured Chain-of-Thought
1. Definire struttura della classe e del metodo.
2. Mappare i parametri in uscita verso `<c-interprete>`.
3. Gestire eventuali errori di invocazione e logging.
