---
id: UNF-413-TASK-9
UNF: UNF-413
title: UNF-413-TASK-9 - Implementazione componente dc_viaggiForm
type: feature
priority: high
component: disambiguation-interface
status: TODO
depends_on: [UNF-413-TASK-4]
blocked_by: []
owner: jean.sanchez
effort: 4
estimated_time: 4h
tags: [salesforce, lwc, puviaggi]
---

# dc_viaggiForm

## Descrizione
Form a due step per l'ambito `PUVIAGGI`. Primo step: dropdown del paese di destinazione (da domini). Secondo step: range date di viaggio (andata e ritorno) tramite `dc_datePicker`, numero viaggiatori e disclaimer età (sotto 80 anni).

## Motivazione
Permette di predisporre le informazioni necessarie per la polizza viaggio includendo le date del soggiorno.

## Criteri di Accettazione
- [ ] Caricamento dinamico dei paesi dal servizio metadata.
- [ ] Range date con validazione `>= oggi` per la data di partenza.
- [ ] Inserimento numero viaggiatori limitato a 1-4.
- [ ] Output `referencesToUpdate` per `PUVIAGGI`.

## Structured Chain-of-Thought
1. Creare form multi-step con dropdown paesi e range date.
2. Validare numero viaggiatori e mostrare disclaimer sui limiti di età.
3. Serializzare i dati nel payload secondo lo schema previsto.
