---
id: UNF-413-TASK-2
UNF: UNF-413
title: UNF-413-TASK-2 - Implementazione componente dc_selectorProtezione
type: feature
priority: high
component: disambiguation-interface
status: TODO
depends_on: [UNF-413-TASK-1]
blocked_by: []
owner: jean.sanchez
effort: 3
estimated_time: 3h
tags: [salesforce, lwc, dynamic-ui]
---

# dc_selectorProtezione

## Descrizione
Sviluppare il componente LWC `dc_selectorProtezione` che mostra un dropdown dinamico degli ambiti di bisogno recuperati tramite web service `Retrieve Ambiti di Bisogno`. Il componente deve emettere l'evento `onAmbitoSelected({code})` al cambio di selezione.

## Motivazione
Questo componente è la base della disambiguazione e permette di scegliere l'ambito di protezione da cui dipenderanno i form successivi.

## Criteri di Accettazione
- [ ] Il dropdown popola le opzioni tramite chiamata WS.
- [ ] Mostra la label corretta per ogni ambito (es. "Cane e Gatto", "Casa", ecc.).
- [ ] Alla selezione emette l'evento `onAmbitoSelected` con il codice dell'ambito.

## Structured Chain-of-Thought
1. Definire struttura LWC con wire al metodo Apex per il recupero ambiti.
2. Implementare la chiamata al servizio e la gestione loading/error.
3. Gestire l'evento di selezione ed emetterlo verso i componenti genitori.
