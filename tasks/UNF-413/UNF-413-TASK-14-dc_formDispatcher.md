---
id: UNF-413-TASK-14
UNF: UNF-413
title: UNF-413-TASK-14 - Implementazione componente dc_formDispatcher
type: feature
priority: high
component: disambiguation-interface
status: TODO
depends_on: [UNF-413-TASK-2, UNF-413-TASK-5, UNF-413-TASK-6, UNF-413-TASK-7, UNF-413-TASK-8, UNF-413-TASK-9, UNF-413-TASK-10]
blocked_by: []
owner: jean.sanchez
effort: 3
estimated_time: 3h
tags: [salesforce, lwc]
---

# dc_formDispatcher

## Descrizione
Componente che, in base all'ambito selezionato, renderizza dinamicamente il form corrispondente (`dc_petForm`, `dc_infortuniForm`, `dc_veicoliForm`, ecc.). Utilizza la tecnica del lazy load per caricare solo il componente necessario.

## Motivazione
Centralizza la logica di routing dei form e migliora le performance caricando on-demand i componenti specifici.

## Criteri di Accettazione
- [ ] Rendering dinamico dei form in base al `selectedAmbito` ricevuto.
- [ ] Lazy load dei componenti per ridurre il bundle iniziale.
- [ ] Propagazione degli eventi dei singoli form al componente padre.

## Structured Chain-of-Thought
1. Mappare il codice ambito ai componenti corrispondenti.
2. Gestire il caricamento dinamico (`import()` asynchrono) dei componenti.
3. Assicurare che i dati raccolti vengano passati correttamente allo step finale.
