---
id: UNF-413-TASK-6
UNF: UNF-413
title: UNF-413-TASK-6 - Implementazione componente dc_infortuniForm
type: feature
priority: high
component: disambiguation-interface
status: TODO
depends_on: [UNF-413-TASK-4]
blocked_by: []
owner: jean.sanchez
effort: 5
estimated_time: 5h
tags: [salesforce, lwc, puinfortuni]
---

# dc_infortuniForm

## Descrizione
Form a due step per l'ambito `PUINFORTUNI`. Nel primo step si raccolgono Codice Fiscale con validazione formale e data effetto. Nel secondo step professione (picklist), occupazione (dipendente dalla professione), indirizzo residenza tramite `dc_addressSearch` e status familiare.

## Motivazione
Gli infortuni richiedono dati anagrafici e di residenza precisi per la corretta quotazione della polizza.

## Criteri di Accettazione
- [ ] Validazione formale del Codice Fiscale.
- [ ] Dipendenza dinamica professione → occupazione.
- [ ] Utilizzo di `dc_addressSearch` per la residenza.
- [ ] Output `referencesToUpdate` completo per `PUINFORTUNI`.

## Structured Chain-of-Thought
1. Implementare wizard a due step con controllo dell'avanzamento.
2. Caricare domini professione e occupazione da `MetadataService`.
3. Integrare address search e serializzare i dati nel payload finale.
