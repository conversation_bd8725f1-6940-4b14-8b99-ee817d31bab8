---
id: UNF-413-TASK-5
UNF: UNF-413
title: UNF-413-TASK-5 - Implementazione componente dc_petForm
type: feature
priority: high
component: disambiguation-interface
status: TODO
depends_on: [UNF-413-TASK-4]
blocked_by: []
owner: jean.sanchez
effort: 3
estimated_time: 3h
tags: [salesforce, lwc, pupet]
---

# dc_petForm

## Descrizione
Realizzare il form specifico per l'ambito `PUPET` con campi: tipologia animale (cane/gatto), età dinamica da metadati e data effetto. Deve restituire `referencesToUpdate` con i valori raccolti.

## Motivazione
Gestisce in modo mirato le informazioni necessarie per le polizze dedicate agli animali domestici.

## Criteri di Accettazione
- [ ] Caricamento dinamico del dominio età animale.
- [ ] Validazione e raccolta dei campi previsti.
- [ ] Output JSON conforme a `referencesToUpdate`.

## Structured Chain-of-Thought
1. Definire struttura LWC con picklist per tipologia e età.
2. Util<PERSON>zare `dc_datePicker` per la data effetto.
3. Serializzare i dati in `referencesToUpdate` da inviare al servizio.
