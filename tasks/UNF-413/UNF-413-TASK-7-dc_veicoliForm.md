---
id: UNF-413-TASK-7
UNF: UNF-413
title: UNF-413-TASK-7 - Implementazione componente dc_veicoliForm
type: feature
priority: high
component: disambiguation-interface
status: TODO
depends_on: [UNF-413-TASK-4]
blocked_by: []
owner: jean.sanchez
effort: 4
estimated_time: 4h
tags: [salesforce, lwc, puveicolo]
---

# dc_veicoliForm

## Descrizione
Componente specifico per l'ambito `PUVEICOLO` con campi tipo veicolo, targa (opzionale se `senzaTarga`), data di nascita, residenza e checkbox "Recupero Classe da altro veicolo".

## Motivazione
Raccoglie le informazioni essenziali per la quotazione di polizze veicoli, con gestione della targa opzionale.

## Criteri di Accettazione
- [ ] Caricamento tipi veicolo da domini dinamici.
- [ ] Campo targa visibile solo se `senzaTarga` non selezionato.
- [ ] Raccolta residenza tramite `dc_addressSearch`.
- [ ] Output `referencesToUpdate` per `PUVEICOLO`.

## Structured Chain-of-Thought
1. Predisporre i picklist e la logica di visibilità della targa.
2. Utilizzare `dc_addressSearch` per la residenza.
3. Serializzare i valori e gestire il flag di recupero classe.
