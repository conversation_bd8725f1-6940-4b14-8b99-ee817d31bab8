---
id: UNF-413-TASK-3
UNF: UNF-413
title: UNF-413-TASK-3 - Implementazione componente dc_addressSearch
type: feature
priority: high
component: disambiguation-interface
status: TODO
depends_on: [UNF-413-TASK-2]
blocked_by: []
owner: jean.sanchez
effort: 5
estimated_time: 4h
tags: [salesforce, lwc, google-api]
---

# dc_addressSearch

## Descrizione
Realizzare il componente `dc_addressSearch` che integra Google Places Autocomplete per la ricerca indirizzo e consente l'inserimento manuale dei dati (via, civico, cap, città, provincia, stato). In modalità manuale deve invocare `AddressService.normalizeAddress()` via Apex restituendo il JSON normalizzato.

## Motivazione
La corretta normalizzazione dell'indirizzo è necessaria per garantire coerenza dei dati e successivo invio all'interprete.

## Criteri di Accettazione
- [ ] Autocomplete con Google Places funzionante.
- [ ] Possibilità di switch a inserimento manuale con tutti i campi richiesti.
- [ ] Invocazione Apex di normalizzazione indirizzo e restituzione del risultato.

## Structured Chain-of-Thought
1. Configurare Google Places Autocomplete nel componente.
2. Gestire il toggle tra ricerca e inserimento manuale.
3. Implementare la chiamata al servizio di normalizzazione in Apex e restituire il JSON al parent.
