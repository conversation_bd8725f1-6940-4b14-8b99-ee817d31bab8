---
id: UNF-413-TASK-16
UNF: UNF-413
title: UNF-413-TASK-16 - Implementazione validazioni e logiche comuni
type: feature
priority: medium
component: disambiguation-interface
status: TODO
depends_on: [UNF-413-TASK-5, UNF-413-TASK-6, UNF-413-TASK-7, UNF-413-TASK-8, UNF-413-TASK-9, UNF-413-TASK-10]
blocked_by: []
owner: jean.sanchez
effort: 2
estimated_time: 2h
tags: [salesforce, lwc]
---

# Validazioni e logiche comuni

## Descrizione
Consolidare in un modulo condiviso le validazioni comuni (data decorrenza ≥ oggi, controllo formale codice fiscale, dipendenza professione → occupazione, visibilità condizionale dei campi come targa o pannelli solari).

## Motivazione
Centralizzare le regole evita duplicazioni e garantisce coerenza tra i vari form.

## Criteri di Accettazione
- [ ] Implementazione helper o mixin riutilizzabile dai vari componenti.
- [ ] Gestione di tutte le regole comuni descritte nel requisito.
- [ ] Documentazione dell'utilizzo nei form specifici.

## Structured Chain-of-Thought
1. Individuare le regole comuni da estrarre.
2. Implementare funzioni di validazione e metodi di supporto.
3. Applicare tali funzioni nei form interessati e aggiornare gli esempi.
