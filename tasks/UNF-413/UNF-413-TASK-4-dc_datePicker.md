---
id: UNF-413-TASK-4
UNF: UNF-413
title: UNF-413-TASK-4 - Implementazione componente dc_datePicker
type: feature
priority: high
component: disambiguation-interface
status: TODO
depends_on: [UNF-413-TASK-2]
blocked_by: []
owner: jean.sanchez
effort: 2
estimated_time: 2h
tags: [salesforce, lwc]
---

# dc_datePicker

## Descrizione
Creare il componente `dc_datePicker` con validazione che la data selezionata sia maggiore o uguale a oggi. Deve supportare sia una singola data sia un intervallo di date (per l'ambito viaggi) e inviare l'evento `onDateSelected(dateValue)`.

## Motivazione
Molte polizze richiedono la data di decorrenza. La gestione centralizzata delle regole di validazione semplifica i form specifici.

## Criteri di Accettazione
- [ ] Blocco selezione date precedenti a oggi.
- [ ] Supporto a range di date opzionale.
- [ ] Emissione evento `onDateSelected` con valore scelto.

## Structured Chain-of-Thought
1. Creare il componente base con `lightning-input` di tipo `date`.
2. Aggiungere logica di validazione e gestione range.
3. Testare l'evento con valori singoli e intervallo.
