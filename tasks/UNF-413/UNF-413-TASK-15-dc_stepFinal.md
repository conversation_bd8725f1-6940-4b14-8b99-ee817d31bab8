---
id: UNF-413-TASK-15
UNF: UNF-413
title: UNF-413-TASK-15 - Implementazione componente dc_stepFinal
type: feature
priority: high
component: disambiguation-interface
status: TODO
depends_on: [UNF-413-TASK-11, UNF-413-TASK-14]
blocked_by: []
owner: jean.sanchez
effort: 3
estimated_time: 3h
tags: [salesforce, lwc]
---

# dc_stepFinal

## Descrizione
Componente finale che presenta il bottone `Prosegui`. Raccoglie i dati validati dal form corrente, genera il payload conforme allo schema richiesto e chiama `invokeInterpreter(payload)` esponendo un evento per la navigazione al passo successivo o la visualizzazione delle offerte.

## Motivazione
Esegue l'invocazione definitiva verso l'interprete integrando tutti i dati raccolti e gestisce l'avanzamento del processo.

## Criteri di Accettazione
- [ ] Raccolta e validazione dei dati dal form corrente.
- [ ] Invocazione di `dc_DisambiguationService.invokeInterpreter` con payload completo.
- [ ] Emissione evento di successo o gestione errori per la navigazione.

## Structured Chain-of-Thought
1. Recuperare i dati serializzati dal form dispatcher.
2. Creare il payload con `action`, `productType`, `env` e `referencesToUpdate`.
3. Gestire la promessa ritornata dal servizio ed emettere l'evento di completamento.
