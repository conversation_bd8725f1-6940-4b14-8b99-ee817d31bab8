---
id: UNF-413-TASK-8
UNF: UNF-413
title: UNF-413-TASK-8 - Implementazione componenti dc_casaForm e dc_famigliaForm
type: feature
priority: high
component: disambiguation-interface
status: TODO
depends_on: [UNF-413-TASK-4]
blocked_by: []
owner: jean.sanchez
effort: 4
estimated_time: 4h
tags: [salesforce, lwc, pucasa, pufamiglia]
---

# dc_casaForm e dc_famigliaForm

## Descrizione
Creare i form per gli ambiti `PUCASA` e `PUFAMIGLIA` con campi comuni di tipologia abitazione, residenza e data effetto. Nel form casa deve essere presente la checkbox "Hai pannelli solari?" visibile solo quando la tipologia è `Villa`.

## Motivazione
Consentono la raccolta dati per le polizze casa e famiglia, gestendo campi condizionali specifici.

## Criteri di Accettazione
- [ ] Caricamento dinamico delle tipologie di abitazione.
- [ ] Residenza tramite `dc_addressSearch`.
- [ ] Data effetto con `dc_datePicker`.
- [ ] Checkbox pannelli solari visibile solo per tipologia `Villa`.
- [ ] Output `referencesToUpdate` per `PUCASA` e `PUFAMIGLIA`.

## Structured Chain-of-Thought
1. Predisporre componenti riutilizzabili per i campi comuni.
2. Gestire la visibilità condizionale della checkbox in `dc_casaForm`.
3. Serializzare i dati per entrambi gli ambiti secondo lo schema previsto.
