---
id: UNF-413-TASK-12
UNF: UNF-413
title: UNF-413-TASK-12 - Creazione Apex dc_AddressService
type: feature
priority: high
component: disambiguation-interface
status: TODO
depends_on: [UNF-413-TASK-3]
blocked_by: []
owner: jean.sanchez
effort: 2
estimated_time: 2h
tags: [salesforce, apex]
---

# dc_AddressService

## Descrizione
Sviluppare la classe Apex `dc_AddressService.cls` con metodo `normalizeAddress(String via, String cap, String comune, String provincia)` che effettua la chiamata REST `POST /api/pub/indirizzi/v2/normalizza` e restituisce l'oggetto `AddressResult`.

## Motivazione
Centralizza la logica di normalizzazione dell'indirizzo per tutti i componenti che richiedono questa funzionalità.

## Criteri di Accettazione
- [ ] Chiamata al servizio REST `normalizza` correttamente implementata.
- [ ] Gestione della risposta e mappatura nell'oggetto `AddressResult`.
- [ ] Gestione errori di rete o formattazione.

## Structured Chain-of-Thought
1. Definire il metodo e il modello di `AddressResult`.
2. Implementare la chiamata HTTP POST con gestione header e body.
3. Restituire l'oggetto normalizzato ai componenti LWC.
