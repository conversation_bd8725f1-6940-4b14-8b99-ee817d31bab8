<template>
  <div class={componentClass} onclick={handleClick}>
    <template if:true={isIcon}>
      <div class="tooltip-container">
        <template if:true={isLabelVisible}>
          <span>{field.label}</span>
        </template>
        <div class={resource} style={responsiveSizeFormattedLabel}></div>
      </div>
    </template>

    <template if:true={isImage}>
      <img src={imgMobile} class="visibility-src-mobile" alt="Immagine versione mobile" />
      <img src={imgTablet} class="visibility-src-tablet" alt="Immagine versione tablet" />
      <img src={imgDesktop} class="visibility-src-desktop" alt="Immagine versione desktop" />
    </template>
  </div>

  <template if:true={isTooltipVisible}>
    <c-tooltip onclosetooltip={handleCloseTooltip} field={toolpData}></c-tooltip>
  </template>
</template>