import { errors } from 'c/errorMessage';
import { checkPatternForType, getFormState, markFieldTouched, patterns, setFieldValue } from 'c/formState';
import { fireEvent, unregisterListener, registerListener } from 'c/pubsub';
import { utils } from 'c/utils';
import { utilsPega } from 'c/utilsPega';
import { LightningElement, api, track } from 'lwc';

export default class FieldInputText extends LightningElement {
  _field;
  effectiveIsRequired;
  firstTouch = true;
  @api decodedValue = '';
  @api parentLayout;
  @api debug;

  @track isValid = true;
  @track errorMessage = '';
  ;
  @api
  get field() {
    return this._field;
  }

  set field(value) {
    this._field = value;
    const isEffectivelyVisible = value && value.visible !== false;
    const isPegaRequired = value && (value.required === true || value.customAttributes?.required === 'true');
    this.effectiveIsRequired = isEffectivelyVisible && isPegaRequired;

    setFieldValue(
      value?.reference,
      value?.value,
      value?.customAttributes?.validation || 'text',
      this.effectiveIsRequired
    );
  }



  disconnectedCallback() {
    if (this._field && this._field.reference) {
      setFieldValue(
        this._field.reference,
        undefined,
        this._field.customAttributes?.validation || 'text',
        false
      );
    } else {
    }

    // TODO DA SPOSTARE????
    unregisterListener('triggerValidation', this.handleExternalValidation);
  }
  get label() {
    return this.field?.label ? utils.decodeHTML(this.field.label) : '';
  }

  get format() {
    if (this.field.labelFormat) return utils.getClassFromFormat(this.field.labelFormat);
  }

  get labelFormat() {
    return this.field.labelFormat;
  }

  get tooltip() {
    let tooltip = '';
    if (this.field.control.modes?.[0]?.tooltip) {
      tooltip = this.field.control.modes[0].tooltip;
    }
    return utils.decodeHTML(tooltip);
  }

  get placeholder() {
    let placeholder;
    placeholder = this.field?.control?.modes?.[0]?.placeholder;
    return placeholder;
  }

  get readonly() {
    return this.field.readOnly === true;
  }

  get isDisabled() {
    return this.field.disabled;
  }

  get formatReadWriteType() {
    let type = 'text';
    let pattern = null;
    let errorMessage = '';
    const state = getFormState();
    const reference = this.field?.reference;
    const value = state.values[reference]?.value ?? '';
    const customAttributes = this.field?.customAttributes || {};
    const formatType = this.field?.customAttributes?.validation || 'text';
    const taxCode = state.values[customAttributes.referenceCF]?.value ?? '';
    const { errorMessage: validationErrorMessage } = checkPatternForType('', formatType);
    errorMessage = validationErrorMessage;

    const inputTypes = {
      number: 'number',
      houseNumber: 'text',
      email: 'text',
      mobilephone: 'tel',
      taxCode: 'text',
    };

    type = inputTypes[formatType] || 'text';
    pattern = patterns[formatType];

    if (pattern && value) {
      const regex = new RegExp(pattern);
      if (!regex.test(value)) {
        this.isValid = false;
        return { type, pattern, errorMessage: errors[formatType] };
      }
    }

    if (!this.field.control || !this.field.control.modes[0].formatType) return { type, pattern };

    if (
      customAttributes?.validation === 'taxCode' &&
      customAttributes.nameReference &&
      customAttributes.surnameReference
    ) {
      const fieldsError = [
        this.cfWithNameAndSurname(customAttributes.nameReference, 'name'),
        this.cfWithNameAndSurname(customAttributes.surnameReference, 'surname'),
      ].filter(Boolean);

      this.isValid = fieldsError.length === 0;
      //se risulta valido controllo che il CF sia popolato
      if(!this.firstTouch && this.isValid){
        this.isValid = value ? true : false; //se il cf non è popolato isValid non può rimanere true
      }
      if (fieldsError.length > 0) {
        errorMessage = fieldsError.map(err => errors[err]).join(' ');
      }
    }
    else if (
      (customAttributes.validation === 'nameWithCF' || customAttributes.validation === 'surnameWithCF') &&
      taxCode &&
      value
    ) {
      const cfType = customAttributes?.validation === 'nameWithCF' ? 'name' : 'surname';
      const isValid = utilsPega.customAttributes.checkNameSurnameOnFiscalCode(taxCode, value, cfType);

      if (isValid) {
        this.isValid = true;
        errorMessage = '';
        this.triggerCrossFieldValidation(customAttributes.referenceCF);
      } else {
        this.isValid = false;
        errorMessage = errors[cfType === 'name' ? 'errorNameCF' : 'errorSurnameCF'];
      }
    }

    // campo obbligatorio ma non metto isValid = false 
    if(!this.readonly && this.effectiveIsRequired && !value){
       errorMessage= errors['required'];
    }
    return { type, pattern, errorMessage };
  }

  get componentClass() {
    return `pxTextInput ${this.debug ? 'debug' : ''}`.trim();
  }

  get textInputClass() {
    return `input-dati-utente ${this.field.disabled ? 'disable-input' : ''} ${this.isValid ? '' : 'invalid-input'}`.trim();
  }

  cfWithNameAndSurname(refToCompare, type) {
    const state = getFormState();
    const reference = this.field?.reference;
    const taxCodeError = type === 'name' ? 'taxCodeName' : 'taxCodeSurname';

    const cfValue = state.values[reference]?.value;
    const nameOrSurnameValue = state.values[refToCompare]?.value;

    if (!cfValue || !nameOrSurnameValue) {
      return null;
    }

    const isValid = utilsPega.customAttributes.checkNameSurnameOnFiscalCode(
      cfValue,
      nameOrSurnameValue,
      type
    );

    if (!isValid) {
      return taxCodeError;
    }

    return null;
  }

  triggerCrossFieldValidation(fieldReference) {
    if (!fieldReference) return;

    setTimeout(() => {
      const event = new CustomEvent('crossfieldvalidation', {
        detail: { fieldReference },
        bubbles: true,
        composed: true
      });
      this.dispatchEvent(event);
    }, 0);
  }



  handleInputChange(evt) {
    this.decodedValue = evt.target.value;
    this.firstTouch = false;
    setFieldValue(
      this.field.reference,
      this.decodedValue,
      this.field?.customAttributes?.validation || 'text',
      this.field.required || this.field?.customAttributes?.required
    );

    const { errorMessage } = this.formatReadWriteType;
    this.errorMessage = errorMessage;

    const container = this.template.querySelector('.input-dati-utente');
    if (container) {
      container.classList.toggle('invalid-input', !!errorMessage);
    }

    this.triggerCrossFieldValidationForRelatedFields();

    fireEvent('handleFieldChanged', {
      evt: evt,
      field: this.field,
      parentLayout: this.parentLayout,
    });
  }

  fieldTouched() {
    markFieldTouched(this.field.reference);
    this.firstTouch = false;
    const container = this.template.querySelector('.input-dati-utente');
    const state = getFormState();
    const reference = this.field?.reference;
    const touched = state.touchedFields.has(reference);

    if (!touched) return;

    const isRequired = state.requiredFields.has(reference);
    const isTouched = state.touchedFields.has(reference);
    const value = state.values[reference]?.value ?? '';

    if (isRequired && isTouched && value === '') {
      container.classList.add('invalid-input');
      this.isValid = false;
    } else {
      container.classList.remove('invalid-input');
      this.isValid = true;
    }
  }

  connectedCallback() {
    this.registerValidationListener();
    this.registerCrossFieldValidationListener();
  }

  registerValidationListener() {
    registerListener('triggerValidation', this.handleExternalValidation);
  }

  registerCrossFieldValidationListener() {
    this.template.addEventListener('crossfieldvalidation', this.handleCrossFieldValidation);
  }

  disconnectedCallback() {
    unregisterListener('triggerValidation', this.handleExternalValidation);
    this.template.removeEventListener('crossfieldvalidation', this.handleCrossFieldValidation);
    
    //elimina la reference dal formstate
    setFieldValue( 
      this.field.reference,
      '',
      '',
      false
    );

    //elimina la reference in caseData dall'interprete
    fireEvent('handlerDeleteValue', { 
      field: this.field,
      parentLayout: this.parentLayout,
    });
  }

  handleExternalValidation = () => {
    this.fieldTouched();
  };

  handleCrossFieldValidation = (event) => {
    const { fieldReference } = event.detail;

    if (fieldReference === this.field?.reference) {
      const { errorMessage } = this.formatReadWriteType;
      this.errorMessage = errorMessage;

      const container = this.template.querySelector('.input-dati-utente');
      if (container) {
        container.classList.toggle('invalid-input', !!errorMessage);
      }
    }
  };

  triggerCrossFieldValidationForRelatedFields() {
    const customAttributes = this.field?.customAttributes || {};
    if (customAttributes.validation === 'nameWithCF' || customAttributes.validation === 'surnameWithCF') {
      if (customAttributes.referenceCF) {
        this.triggerCrossFieldValidation(customAttributes.referenceCF);
      }
    }

    if (customAttributes.validation === 'taxCode') {
      if (customAttributes.nameReference) {
        this.triggerCrossFieldValidation(customAttributes.nameReference);
      }
      if (customAttributes.surnameReference) {
        this.triggerCrossFieldValidation(customAttributes.surnameReference);
      }
    }
  };
}