import { errors } from 'c/errorMessage';
import { getFormState, markFieldTouched, setFieldValue } from 'c/formState';
import { fireEvent } from 'c/pubsub';
import { utils } from 'c/utils';
import { LightningElement, api, track } from 'lwc';

export default class FieldDropdown extends LightningElement {
  _field;

  @api debug;
  @api disabled;
  @track selectedValue;
  @track opened = false;
  @track isValid = true;

  @api
  get field() {
    return this._field;
  }

  set field(value) {
    this._field = value;
    const defaultValue = this._field?.value || '';
    const selectedOption = value.control.modes[0].options.find((opt) => opt.key === value.value);
    this.selectedValue = selectedOption ? selectedOption?.value : defaultValue;

    setFieldValue(
      value.reference,
      this.selectedValue,
      value?.customAttributes?.validation || 'text',
      value.required || value?.customAttributes?.required
    );
  }

  get label() {
    return this.field?.label ? utils.decodeHTML(this.field.label) : '';
  }

  get format() {
    if (this.field.labelFormat) return utils.getClassFromFormat(this.field.labelFormat);
  }

  get labelFormat() {
    return this.field.labelFormat;
  }

  get options() {
    if (this.field && this.field.control && this.field.control.modes) {
      return this.field.control.modes[0].options.map((option) => ({
        label: option.value,
        value: option.key,
      }));
    }
    return [];
  }

  get placeholder() {
    if (this.field && this.field.control && this.field.control.modes) {
      return this.field.control.modes[0].placeholder;
    }
    return 'Scegli un opzione';
  }

  get format() {
    if (this.field.labelFormat) return utils.getClassFromFormat(this.field.labelFormat);
  }

  get componentClass() {
    return `pxDropdown ${this.debug ? 'debug' : ''}`.trim();
  }

  get listClasses() {
    return `select-options us-select-wrapper withLabel ${this.opened ? 'opened' : ''} `;
  }

  get dropdownClasses() {
    return `select-styled ${this.disabled ? 'isDisabled disabled-input-box' : ''}`;
  }

  get options() {
    if (this.field && this.field.control && this.field.control.modes) {
      return this.field.control.modes[0].options.map((option) => ({
        label: option.value,
        value: option.key,
        className: `us-option-wrapper ${option.key === this.selectedValue ? 'active' : ''}`.trim(),
      }));
    }
    return [];
  }

  get showLikeLabel() {
    return (this.field.customAttributes && this.field.customAttributes?.showLikeLabel) || this.field.readOnly
      ? true
      : false;
  }

  get selectedOptionValue() {
    return this.selectedValue;
  }

  get errorMessage() {
    let message;

    //if (!this.isValid && this.field.value === '') {
    if (!this.isValid && this.selectedValue === '') {
      message = errors['required'];
    }
    return message;
  }

  dropdownClick() {
    if (this.disabled) return; 

    const container = this.template.querySelector('.select-styled');

    if (this.opened) {
      this.opened = false;
      container.classList.remove('active');
    } else {
      this.opened = true;
      container.classList.add('active');
    }
  }

  handleBlur() {
    if (this.opened) this.opened = false;
  }

  fieldTouched() {
    //this.opened = false;
    markFieldTouched(this.field.reference);

    const container = this.template.querySelector('.tpd_selectAngular');
    const state = getFormState();
    const reference = this.field?.reference;
    const touched = state.touchedFields.has(reference);

    if (!touched) return;

    const isRequired = state.requiredFields.has(reference);
    const isTouched = state.touchedFields.has(reference);
    const value = state.values[reference]?.value ?? '';

    if (isRequired && isTouched && value === '') {
      container.classList.add('invalid-input');
      this.isValid = false;
    } else {
      container.classList.remove('invalid-input');
      this.isValid = true;
    }
  }

  handleOptionSelect(evt) {
    if (this.disabled) return;
    
    const selectedKey = evt.currentTarget.dataset.key;
    const selectedOption = this.options.find((opt) => opt.value === selectedKey);

    if (selectedOption) {
      this.selectedValue = selectedOption.label;
      setFieldValue(
        this.field.reference,
        this.selectedValue,
        this.field?.customAttributes?.validation || 'text',
        this.field.required || this.field?.customAttributes?.required
      );

      // Create synthetic change event with selected value
      const changeEvent = {
        target: {
          value: selectedOption.label,
        },
      };

      // Trigger handleInputChange with updated value
      this.handleInputChange(changeEvent);
    }
  }

  handleInputChange(evt) {
    const newValue = evt.target.value;

    const selectedOption = this.options.find((opt) => opt.label.toLowerCase() === newValue.toLowerCase());
    if (!selectedOption) return;

    const syntheticEvent = {
      target: {
        dataset: {
          reference: this.field.reference,
        },
        value: selectedOption.value,
      },
    };

    fireEvent('handleFieldChanged', {
      evt: syntheticEvent,
      field: this.field,
      parentLayout: this.parentLayout,
    });
  }
}