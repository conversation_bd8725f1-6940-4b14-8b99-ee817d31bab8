<template>
  <div class={componentClass}>
    <p if:true={debug} class="temporaryLabel">FIELD PX DATE TIME:</p>

    <c-custom-text-styles content={label} text-css={labelFormat}></c-custom-text-styles>
    
    <template if:false={readonly}>
      <lightning-input class="dateTime" style={style} type={formatType} disabled={disabled} readonly={readonly}
        placeholder={paceholder} name={value} value={formattedTime} data-reference={field.reference} min={minDate} max={maxDate}
        onchange={handleInputChange}>
      </lightning-input>
    </template>
  </div>
</template>