<template>
    <div class="AssurancePackageContainer growMobile" data-selected={checked}>
        <template if:true={assurancePackageHeader}>
            <span class="AssurancePackageHeader">{assurancePackageHeader}</span>
        </template>

        <div class="AssurancePackageBody">
            <span class="AssurancePackageBodyHead">
                <span class="PackageName">{packageName}</span>
                <template if:true={checkbox}>
                    <c-field-checkbox field={checkbox} onchange={handleCheckboxClick}></c-field-checkbox>
                </template>
            </span>

            <div class="AssurancePackageContent">
                <div class="PrezzoAlMese">{premioRataScontato}
                    <span class="ValorePremioLordoRataSuccessiva">{rataSuccessivaPremioLordoScontato}</span>
                    <span class="LabelAlMese">{packageInstallmentsPeriod}</span>
                    <template if:true={premioAnno}>
                        <span class="PrezzoAnno">
                            {premioAnnoFormatted}
                        </span>
                    </template>
                </div>
                <template if:true={premioLordo}>
                    <div class="PrezzoAllAnno">
                        <span class="PrezzoAnno">{premioLordo}</span>
                        <span class="PrezzoAllAnno">{premioLordoScontato}</span>

                        <template if:true={labelAllAnno}>
                            <span class="LabelAllAnno">{labelAllAnno}</span>
                        </template>
                    </div>
                </template>

                <template if:true={AssurancePackageBody}>
                    <span class="PackageInstallmentsPrice">
                        {PackageInstallmentsPrice}
                        <span class="PrezzoAnnoScontato">
                            {premioScontatoAnnoFormatted}
                        </span>
                    </span>
                </template>
            </div>
        </div>

        <!-- <template if:false={isScontoVisibile}> -->
        <div class="AssurancePackageSconto">
            <span class="LabelSconto">
                {sconto}
            </span>
            <span class="PercentualeSconto">
                {percentualeSconto}
            </span>
        </div>
        <!-- </template> -->


        <div class="AssurancePackageFooter">
            <span class="PackageDescription">
                {packageDescription}
            </span>
        </div>
    </div>
</template>