<template>
  <!-- Debug info -->
  <div style="background: #f0f0f0; padding: 10px; margin: 10px; border: 1px solid #ccc;">
    <h3>Debug Info - DxAgencyLocator</h3>
    <p>areDataReady: {areDataReady}</p>
    <p>hasUserLocation: {hasUserLocation}</p>
    <p>userLocation: {userLocationDebug}</p>
    <p>agenciesList POI count: {agenciesCount}</p>
  </div>

  <template if:true={areDataReady}>
    <c-locator-wrapper step-corrente={stepCorrente} onagencyselected={handleSelezioneAgenzia}
      oninstallerselected={handleSelezioneInstallatore} oncomponentready={handleComponentLoading}
      user-location={userLocation} api-data={agenciesList}>
    </c-locator-wrapper>
  </template>

  <template if:false={areDataReady}>
    <div style="background: #ffeeee; padding: 20px; text-align: center;">
      <p>Caricamento dati in corso...</p>
      <p>Verificare la console per dettagli di debug</p>
    </div>
  </template>
</template>