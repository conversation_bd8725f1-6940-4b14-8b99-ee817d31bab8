<template>
  <!-- Equivalente Angular: *ngIf="this.localhostMock" -->
  <template if:true={localhostMock}>
    <div style="padding: 10px; background: #f0f0f0; margin: 10px;">
      <h3>Mock Testing</h3>
      <lightning-button
        label="Prova mock installatore"
        onclick={handleMockInstallatore}>
      </lightning-button>
      <lightning-button
        label="Prova mock agenzia"
        onclick={handleMockAgenzia}>
      </lightning-button>
    </div>
  </template>

  <!-- Equivalente Angular: *ngIf="this.areDataSetted" -->
  <template if:true={areDataSetted}>
    <c-locator-wrapper
      step-corrente={stepCorrente}
      onagencyselected={handleSelezioneAgenzia}
      oninstallerselected={handleSelezioneInstallatore}
      oncomponentready={handleComponentLoading}>
    </c-locator-wrapper>
  </template>
</template>