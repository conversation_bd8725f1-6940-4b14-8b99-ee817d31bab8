import BffInterprete from 'c/bffInterprete';
import { utils } from 'c/utils';
import { LightningElement, api } from 'lwc';

export default class DxAgencyLocator extends LightningElement {
  _field;
  _groups;
  stepCorrente = 'scelta_agenzia';
  locatorAction = false;
  userLocation = {};
  agenciesList = {
    operationResult: {
      type: '0',
      code: '00',
      message: '',
    },
    poi: [
      {
        delegates: '',
        distance: '0,4',
        closing: '',
        zipCode: '27058',
        phoneNumber: '*********',
        patronalFeast: '',
        divestitureDest: '',
        finitaliaMandate: '0',
        management: "SERVIZI ASSICURATIVI OLTREPO' SRL",
        code: '39680',
        divisionCode: '*********',
        longitude: '9.010861',
        latitude: '44.991943',
        openingTime:
          '||09:00|13:00|15:00|19:00|09:00|13:00|15:00|19:00|09:00|13:00|15:00|19:00|09:00|13:00|15:00|19:00|09:00|13:00|15:00|19:00|09:00|12:30|CHIUSO|CHIUSO|',
        town: 'VOGHERA',
        suspensionDate: '',
        provinceAcronym: 'PV',
        faxNumber: '*********',
        address: 'VIA GIUSEPPE GARIBALDI 37',
        itemType: 'AGE',
        provinceCode: '018',
        brands: '',
        newManagementCode: '',
        email: '<EMAIL>',
        name: 'VOGHERA',
        treatedBranches: 'X|X|X|X|X|X',
        locationIstatCode: '018182',
        VatTaxCode: '02854400187',
      },
      {
        delegates: '',
        distance: '9,6',
        closing: '',
        zipCode: '27045',
        phoneNumber: '*********',
        patronalFeast: '',
        divestitureDest: '',
        finitaliaMandate: '0',
        management: 'GANDINI GIOVANNI',
        code: '39055',
        divisionCode: '*********',
        longitude: '9.12684',
        latitude: '45.015083',
        openingTime:
          '||09:00|12:30|15:00|18:30|09:00|12:30|15:00|18:30|09:00|12:30|15:00|18:30|09:00|12:30|15:00|18:30|09:00|12:30|15:00|18:30|CHIUSO|CHIUSO|CHIUSO|CHIUSO|',
        town: 'CASTEGGIO',
        suspensionDate: '',
        provinceAcronym: 'PV',
        faxNumber: '0383803795',
        address: 'PIAZZA CAVOUR,27',
        itemType: 'AGE',
        provinceCode: '018',
        brands: '',
        newManagementCode: '',
        email: '<EMAIL>',
        name: 'CASTEGGIO',
        treatedBranches: 'X|X|X|X|X|X',
        locationIstatCode: '018037',
        VatTaxCode: 'GNDGNN62A30M109Z',
      },
      {
        delegates: '',
        distance: '9,7',
        closing: '',
        zipCode: '27045',
        phoneNumber: '0383805134',
        patronalFeast: '',
        divestitureDest: '',
        finitaliaMandate: '0',
        management: 'BERTONI CRISTIAN',
        code: '40061',
        divisionCode: '*********',
        longitude: '9.127359',
        latitude: '45.0162',
        openingTime:
          '||09:00|12:00|15:00|18:30|09:00|12:00|15:00|18:30|09:00|12:00|15:00|18:30|09:00|12:00|15:00|18:30|09:00|12:00|15:00|18:30|CHIUSO|CHIUSO|CHIUSO|CHIUSO|',
        town: 'CASTEGGIO',
        suspensionDate: '',
        provinceAcronym: 'PV',
        faxNumber: '0383805134',
        address: 'VIA A.DE GASPERI N°8',
        itemType: 'AGE',
        provinceCode: '018',
        brands: '',
        newManagementCode: '',
        email: '<EMAIL>',
        name: 'CASTEGGIO',
        treatedBranches: 'X|X|X|X|X|X',
        locationIstatCode: '018037',
        VatTaxCode: 'BRTCST70S18G388X',
      },
      {
        delegates: '',
        distance: '15,5',
        closing: '',
        zipCode: '15057',
        phoneNumber: '0131861203',
        patronalFeast: '',
        divestitureDest: '',
        finitaliaMandate: '0',
        management: 'G.L.M. ASSICURAZIONI S.R.L.',
        code: '65157.1',
        divisionCode: '*********',
        longitude: '8.867258',
        latitude: '44.89746',
        openingTime: '',
        town: 'TORTONA',
        suspensionDate: '',
        provinceAcronym: '',
        faxNumber: '0131814802',
        address: 'VIA MONTEMERLO 17',
        itemType: 'AGE',
        provinceCode: '',
        brands: '',
        newManagementCode: '',
        email: '<EMAIL>',
        name: 'SEDE SECONDARIA TORTONA',
        treatedBranches: '',
        locationIstatCode: '006174',
        VatTaxCode: '',
      },
      {
        delegates: '',
        distance: '16',
        closing: '',
        zipCode: '15057',
        phoneNumber: '0131822705',
        patronalFeast: '',
        divestitureDest: '',
        finitaliaMandate: '0',
        management: 'INTESA S.R.L.',
        code: '39432',
        divisionCode: '*********',
        longitude: '8.859666',
        latitude: '44.896492',
        openingTime:
          '||08:00|13:00|14:30|17:00|08:00|13:00|14:30|17:00|08:00|13:00|14:30|17:00|08:00|13:00|14:30|17:00|08:00|15:00|CHIUSO|CHIUSO|CHIUSO|CHIUSO|CHIUSO|CHIUSO|',
        town: 'TORTONA',
        suspensionDate: '',
        provinceAcronym: 'AL',
        faxNumber: '0131862644',
        address: 'CORSO REPUBBLICA 8',
        itemType: 'AGE',
        provinceCode: '006',
        brands: '',
        newManagementCode: '',
        email: '<EMAIL>',
        name: 'TORTONA',
        treatedBranches: 'X|X|X|X|X|X',
        locationIstatCode: '006174',
        VatTaxCode: '02573250061',
      },
      {
        delegates: '',
        distance: '16',
        closing: '',
        zipCode: '15057',
        phoneNumber: '0131/815078',
        patronalFeast: '',
        divestitureDest: '',
        finitaliaMandate: '0',
        management: 'ASSICURAZIONI PICCHI S.N.C. DI MARCO PICCHI & C.',
        code: '64458',
        divisionCode: '*********',
        longitude: '8.862394',
        latitude: '44.895077',
        openingTime:
          '||08:30|12:30|14:30|18:00|08:30|12:30|14:30|18:00|08:30|12:30|14:30|18:00|08:30|12:30|14:30|18:00|08:30|12:30|14:30|18:00|CHIUSO|CHIUSO|CHIUSO|CHIUSO|',
        town: 'TORTONA',
        suspensionDate: '',
        provinceAcronym: 'AL',
        faxNumber: '0131820392',
        address: 'LARGO BORGARELLI',
        itemType: 'AGE',
        provinceCode: '006',
        brands: '',
        newManagementCode: '',
        email: '<EMAIL>',
        name: 'TORTONA',
        treatedBranches: 'X|X|X|X|X|X',
        locationIstatCode: '006174',
        VatTaxCode: '02491210064',
      },
      {
        delegates: '',
        distance: '18,1',
        closing: '',
        zipCode: '27051',
        phoneNumber: '0382454547',
        patronalFeast: '',
        divestitureDest: '',
        finitaliaMandate: '0',
        management: 'ASSICURAZIONI BORDIGA E LOSSANI DI BORDIGA DINA E LOSSANI ANDREA S.N.C.',
        code: '61413',
        divisionCode: '*********',
        longitude: '9.109198',
        latitude: '45.141132',
        openingTime:
          '||09:00|12:00|16:00|18:00|09:00|12:00|16:00|18:00|09:00|12:00|16:00|18:00|09:00|12:00|16:00|18:00|09:00|12:00|16:00|18:00|09:00|12:00|14:00|17:00|',
        town: 'CAVA MANARA',
        suspensionDate: '',
        provinceAcronym: 'PV',
        faxNumber: '0382454547',
        address: 'V. GRAMSCI 25-1° PIANO',
        itemType: 'AGE',
        provinceCode: '018',
        brands: '',
        newManagementCode: '',
        email: '<EMAIL>',
        name: 'CAVA MANARA',
        treatedBranches: 'X|X|X|X|X|X',
        locationIstatCode: '018041',
        VatTaxCode: '02111900185',
      },
      {
        delegates: '',
        distance: '20,3',
        closing: '',
        zipCode: '27027',
        phoneNumber: '0382815185',
        patronalFeast: '',
        divestitureDest: '',
        finitaliaMandate: '0',
        management: 'ASSICURAZIONI COLTELLI S.N.C. DI COLTELLI STEFANO E COLTELLI CRISTIANO',
        code: '60561',
        divisionCode: '*********',
        longitude: '8.991329',
        latitude: '45.17722',
        openingTime:
          '||08:30|12:30|15:00|18:30|08:30|12:30|15:00|18:30|08:30|12:30|15:00|18:30|08:30|12:30|15:00|18:30|08:30|12:30|15:00|18:30|CHIUSO|CHIUSO|CHIUSO|CHIUSO|',
        town: 'GROPELLO CAIROLI',
        suspensionDate: '',
        provinceAcronym: 'PV',
        faxNumber: '0382815655',
        address: "V. CORTE DELLA SS. TRINITA' 2",
        itemType: 'AGE',
        provinceCode: '018',
        brands: '',
        newManagementCode: '',
        email: '<EMAIL>',
        name: 'GROPELLO CAIROLI',
        treatedBranches: 'X|X|X|X|X|X',
        locationIstatCode: '018076',
        VatTaxCode: '02014470187',
      },
      {
        delegates: '',
        distance: '23',
        closing: '',
        zipCode: '27026',
        phoneNumber: '0382820513',
        patronalFeast: '',
        divestitureDest: '',
        finitaliaMandate: '0',
        management: 'BRACCHI DANIELA',
        code: '61423',
        divisionCode: '*********',
        longitude: '8.923451',
        latitude: '45.19318',
        openingTime:
          '||09:00|12:15|15:00|18:00|09:00|12:15|15:00|18:00|09:00|12:15|15:00|18:00|09:00|12:15|15:00|18:00|09:00|12:15|CHIUSO|CHIUSO|CHIUSO|CHIUSO|CHIUSO|CHIUSO|',
        town: 'GARLASCO',
        suspensionDate: '',
        provinceAcronym: 'PV',
        faxNumber: '0382820513',
        address: 'VIA DE AMICIS 5/2',
        itemType: 'AGE',
        provinceCode: '018',
        brands: '',
        newManagementCode: '',
        email: '<EMAIL>',
        name: 'GARLASCO',
        treatedBranches: 'X|X|X|X|X|X',
        locationIstatCode: '018069',
        VatTaxCode: 'BRCDNL66C44L872H',
      },
      {
        delegates: '',
        distance: '23,5',
        closing: '',
        zipCode: '27100',
        phoneNumber: '0382-1695100',
        patronalFeast: '',
        divestitureDest: '',
        finitaliaMandate: '1',
        management: 'ASSIPAVIA S.R.L.',
        code: '39262',
        divisionCode: '*********',
        longitude: '9.14702',
        latitude: '45.18257',
        openingTime:
          '||08:45|12:30|14:30|18:15|08:45|12:30|14:30|18:15|08:45|12:30|14:30|18:15|08:45|12:30|14:30|18:15|08:45|12:30|14:30|18:15|CHIUSO|CHIUSO|CHIUSO|CHIUSO|',
        town: 'PAVIA',
        suspensionDate: '',
        provinceAcronym: 'PV',
        faxNumber: '0382-1695500',
        address: 'VIA GUIDI,7',
        itemType: 'AGE',
        provinceCode: '018',
        brands: '',
        newManagementCode: '',
        email: '<EMAIL>',
        name: 'PAVIA',
        treatedBranches: 'X|X|X|X|X|X',
        locationIstatCode: '018110',
        VatTaxCode: '02559170184',
      },
    ],
  };
  allAgencies = {};

  @api
  set field(value) {
    this._field = value;
    this._initialize();
  }

  get field() {
    return this._field;
  }

  @api
  set groups(value) {
    this._groups = value;
  }

  get groups() {
    return this._groups;
  }

  async _initialize() {
    console.log('DxAgencyLocator _initialize called');
    if (!this._field) {
      console.log('No field data available');
      return;
    }

    console.log('Getting user location...');
    this.userLocation = await this.getUserLocation();
    console.log('User location result:', this.userLocation);

    // Se non abbiamo la posizione utente, usiamo una posizione mock per il testing
    if (!this.userLocation || !this.userLocation.latitude) {
      console.log('No user location available, using mock location for testing');
      this.userLocation = {
        latitude: '44.991943', // Voghera (stessa delle agenzie mock)
        longitude: '9.010861',
        permission: 'granted'
      };
    }

    if (this.userLocation) {
      console.log('Loading agencies data...');
      const agenciesData = await this._getAgenciesData();
      console.log('Agencies data received:', agenciesData);

      this.agenciesList = agenciesData.closeAgencies || this.agenciesList;
      this.allAgencies = agenciesData.allAgencies || this.allAgencies;

      console.log('Final agencies list:', this.agenciesList);
    }

    const customType = this._field.customAttributes?.customType;
    if (customType === 'WorkshopLocator') {
      this.stepCorrente = 'scelta_installatore';
    }

    if (this._field?.customAttributes?.AgenziaLocator) {
      sessionStorage.setItem(
        'QUOTAZIONE_PEGA',
        JSON.stringify({
          datiAnagrafici: { prodottoLocator: this._field.customAttributes.AgenziaLocator },
        })
      );
    }

    this.locatorAction = this._field?.customAttributes?.componentID === 'locatorSubmitAction';
  }

  get hasUserLocation() {
    const hasLocation = this.userLocation?.latitude && this.userLocation?.longitude;
    console.log('hasUserLocation check:', hasLocation, 'userLocation:', this.userLocation);
    return hasLocation;
  }

  get areDataReady() {
    // Per il testing, rendiamo sempre true se abbiamo almeno i dati mock
    const hasData = this.hasUserLocation || this.agenciesList?.poi?.length > 0;
    console.log('areDataReady check:', hasData, 'hasUserLocation:', this.hasUserLocation, 'has agencies:', this.agenciesList?.poi?.length > 0);
    return hasData;
  }

  // Getter per debug nel template
  get userLocationDebug() {
    return JSON.stringify(this.userLocation);
  }

  get agenciesCount() {
    return this.agenciesList?.poi?.length || 0;
  }

  // Getter per debug nel template
  get userLocationDebug() {
    return JSON.stringify(this.userLocation);
  }

  get agenciesCount() {
    return this.agenciesList?.poi?.length || 0;
  }

  handleComponentLoading() {
    console.log('Component loading completed');
    this.dispatchEvent(new CustomEvent('componentready'));
  }

  async handleSelezioneAgenzia(event) {
    const agency = event.detail;
    const dati = await this._arricchisciDati(agency);

    const dataToSend = {
      'Agenzia.DescrizioneAgenzia': dati.management || dati.name || '-',
      'Agenzia.Telefono': dati.phoneNumber || '-',
      'Agenzia.Email': dati.email || dati.mail || '-',
      'Agenzia.Indirizzo.NomeStrada': dati.address || '-',
      'Agenzia.Indirizzo.Provincia': dati.provinceAcronym || '-',
      'Agenzia.Indirizzo.NumeroCivico': '-',
      'Agenzia.Indirizzo.Cap': dati.zipCode || '-',
      'Agenzia.Indirizzo.Comune': dati.town || '-',
      'Agenzia.CodiceAgenzia': dati.code || '-',
    };

    this.dispatchEvent(new CustomEvent('formchange', { detail: { values: dataToSend } }));

    if (this.locatorAction) {
      this.dispatchEvent(new CustomEvent('nextstep'));
    }
  }

  async handleSelezioneInstallatore(event) {
    const agency = event.detail;
    const dati = await this._arricchisciDati(agency);

    const dataToSend = {
      'GestioneProcesso.ContattiUniboxTemp.InstallatoreUnibox.NomeOfficina': dati.code_ext || '-',
      'GestioneProcesso.ContattiUniboxTemp.InstallatoreUnibox.TelefonoOfficina':
        dati.phoneNumber || '-',
      'GestioneProcesso.ContattiUniboxTemp.InstallatoreUnibox.EmailOfficina':
        dati.email || dati.mail || '-',
      'GestioneProcesso.ContattiUniboxTemp.InstallatoreUnibox.Indirizzo.NomeStrada':
        dati.address || '-',
      'GestioneProcesso.ContattiUniboxTemp.InstallatoreUnibox.Indirizzo.Provincia':
        dati.provinceAcronym || '-',
      'GestioneProcesso.ContattiUniboxTemp.InstallatoreUnibox.Indirizzo.NumeroCivico': '-',
      'GestioneProcesso.ContattiUniboxTemp.InstallatoreUnibox.Indirizzo.Cap': dati.zipCode || '-',
      'GestioneProcesso.ContattiUniboxTemp.InstallatoreUnibox.Indirizzo.Comune': dati.town || '-',
      'GestioneProcesso.ContattiUniboxTemp.InstallatoreUnibox.CodiceOfficina': dati.code || '-',
    };

    this.dispatchEvent(new CustomEvent('formchange', { detail: { values: dataToSend } }));

    if (this.locatorAction) {
      this.dispatchEvent(new CustomEvent('nextstep'));
    }
  }

  async _arricchisciDati(agency) {
    if (!agency || !agency.code) return agency;

    try {
      const response = await fetch(`/poiLocator/v1/poi/${agency.code}`);
      if (!response.ok) throw new Error(`Errore HTTP ${response.status}`);

      const result = await response.json();
      return result || agency;
    } catch (error) {
      console.error("Errore durante l'arricchimento dei dati:", error);
      return agency;
    }
  }

  // async _getAgencies() {
  //   const userLatitude = this.userLocation?.latitude ?? 44.4652;
  //   const userLongitude = this.userLocation?.longitude ?? 9.19;

  //   try {
  //     const bff = new BffInterprete();
  //     const url = bff.getURLS().getCloseAgencies(userLatitude, userLongitude);
  //     const headers = bff.getCustomHeaders();

  //     const response = await utils.apiRequest('GET', url, headers);

  //     return response.slice(0, 10);
  //   } catch (error) {
  //     console.error("Errore durante l'arricchimento dei dati:", JSON.stringify(error));
  //   }
  // }

  // async _getAllAgencies() {
  //   try {
  //     const bff = new BffInterprete();
  //     const url = bff.getURLS().getAllAgencies();
  //     const headers = bff.getCustomHeaders();

  //     const response = await utils.apiRequest('GET', url, headers);

  //     return response.slice(0, 10);
  //   } catch (error) {
  //     console.error("Errore durante l'arricchimento dei dati:", JSON.stringify(error));
  //   }
  // }

  async _getAgenciesData() {
    const userLatitude = this.userLocation?.latitude ?? 44.4652;
    const userLongitude = this.userLocation?.longitude ?? 9.19;
    const bff = new BffInterprete();

    try {
      // Chiamata per le agenzie vicine
      const closeAgenciesUrl = bff.getURLS().getCloseAgencies(userLatitude, userLongitude);
      const headers = bff.getCustomHeaders();
      const closeResponse = await utils.apiRequest('GET', closeAgenciesUrl, headers);

      // Chiamata per tutte le agenzie
      const allAgenciesUrl = bff.getURLS().getAllAgencies();
      const allResponse = await utils.apiRequest('GET', allAgenciesUrl, headers);

      return {
        closeAgencies: closeResponse ? closeResponse.slice(0, 10) : [],
        allAgencies: allResponse ? allResponse.slice(0, 10) : [],
      };
    } catch (error) {
      console.error("Errore durante l'arricchimento dei dati:", JSON.stringify(error));
      return {
        closeAgencies: [],
        allAgencies: [],
      };
    }
  }

  async getUserLocation() {
    if (!navigator.geolocation) {
      console.error('Geolocalizzazione non supportata dal browser.');
      this.userLocation = {
        permission: 'unsupported',
        latitude: null,
        longitude: null,
      };
      return this.userLocation;
    }

    let permissionState = 'default';
    try {
      const result = await navigator.permissions.query({ name: 'geolocation' });
      permissionState = result.state;
    } catch (err) {
      console.warn('Permissions API non disponibile o errore:', err);
    }

    return new Promise((resolve) => {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          this.userLocation = {
            permission: 'granted',
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
          };
          resolve(this.userLocation);
        },
        (error) => {
          console.error('Errore ottenendo la posizione:', error);
          this.userLocation = {
            permission: permissionState === 'denied' ? 'denied' : 'default',
            latitude: null,
            longitude: null,
          };
          resolve(this.userLocation);
        }
      );
    });
  }

  disconnectedCallback() {
    sessionStorage.removeItem('QUOTAZIONE_PEGA');
  }
}
