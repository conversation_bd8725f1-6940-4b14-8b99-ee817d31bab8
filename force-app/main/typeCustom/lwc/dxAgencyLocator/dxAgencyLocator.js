import BffInterprete from 'c/bffInterprete';
import { utils } from 'c/utils';
import { LightningElement, api, track } from 'lwc';

export default class DxAgencyLocator extends LightningElement {
  // Proprietà pubbliche (equivalenti Angular)
  @track stepCorrente = 'scelta_agenzia'; // 'scelta_agenzia' | 'scelta_installatore'
  @track areDataSetted = false;
  @track localhostMock = false;

  // Proprietà private
  _field;
  _groups;
  _lastDataToSend = {};
  _locatorAction = false;
  _bffInterprete = new BffInterprete();

  // Mock data (equivalenti Angular)
  mockAgenzia = {
    code: '39680',
    management: "SERVIZI ASSICURATIVI OLTREPO' SRL",
    name: 'VOGHERA',
    phoneNumber: '038340134',
    email: '<EMAIL>',
    address: 'VIA GIUSEPPE GARIBALDI 37',
    zipCode: '27058',
    town: 'VOGHERA',
    provinceAcronym: 'PV'
  };

  mockInstallatore = {
    code_ext: 'OFF123',
    phoneNumber: '0987654321',
    email: '<EMAIL>',
    address: 'Via Meccanici 10',
    zipCode: '20100',
    town: 'Milano',
    provinceAcronym: 'MI'
  };

  connectedCallback() {
    // Equivalente del constructor Angular
    if (typeof window !== 'undefined') {
      if (window.location.href.includes('localhost')) {
        this.localhostMock = true;
      }
    }
  }

  // Equivalente Angular @Input() set data
  @api
  set field(value) {
    this._field = value;
    if (value) {
      this._processFieldData(value);
    }
  }

  get field() {
    return this._field;
  }

  @api
  set groups(value) {
    this._groups = value;
  }

  get groups() {
    return this._groups;
  }

  // Metodo per processare i dati del field (equivalente Angular)
  _processFieldData(field) {
    const customType = field.customAttributes?.customType || '';

    if (customType === 'WorkshopLocator') {
      this.stepCorrente = 'scelta_installatore';
    } else {
      if (field.customAttributes?.AgenziaLocator) {
        // Equivalente Helpers.SessionHelper.setData
        sessionStorage.setItem('QUOTAZIONE_PEGA', JSON.stringify({
          datiAnagrafici: { prodottoLocator: field.customAttributes.AgenziaLocator }
        }));
      }
    }

    this._locatorAction = field.customAttributes?.componentID === 'locatorSubmitAction';

    // Equivalente _addFormGroupValue({'testValidatore': ''})
    this._addFormGroupValue({ 'testValidatore': '' });
    this.areDataSetted = true;
  }

  // Equivalente Angular handleComponentLoading()
  handleComponentLoading() {
    // In Angular: this._messagesService.setLoading(false);
    // In LWC: dispatchiamo evento per notificare il caricamento completato
    this.dispatchEvent(new CustomEvent('componentready'));
  }

  // Equivalente Angular _arricchisciDati()
  async _arricchisciDati(agency) {
    let esito = agency;
    if (agency) {
      try {
        // Equivalente Angular: new Request(`poiLocator/v1/poi/${agency.code}`, false)
        const url = `${this._bffInterprete.getURLS().getAllAgencies().replace('?category=AGENZIA', '')}/${agency.code}`;
        const headers = this._bffInterprete.getCustomHeaders();
        const response = await utils.apiRequest('GET', url, headers);

        if (response && response.POIArray && response.POIArray[0]) {
          esito = response.POIArray[0];
        }
      } catch (e) {
        console.error("Errore arricchimento dati", String(e));
      }
    }
    return esito;
  }

  // Equivalente Angular _addFormGroupValue()
  _addFormGroupValue(dataToSend) {
    // Rimuoviamo i vecchi controlli
    if (this._lastDataToSend) {
      // In LWC non abbiamo FormGroup, ma possiamo tracciare i dati
      // per compatibilità con il sistema Pega
    }
    this._lastDataToSend = dataToSend;

    // Dispatchiamo evento per aggiornare il form
    this.dispatchEvent(new CustomEvent('formchange', {
      detail: { values: dataToSend }
    }));
  }

  // Equivalente Angular handleSelezioneAgenzia()
  async handleSelezioneAgenzia(event) {
    const agency = event.detail;
    const dati = await this._arricchisciDati(agency);

    const dataToSend = {
      'Agenzia.DescrizioneAgenzia': dati.management || dati.name || '-',
      'Agenzia.Telefono': dati.phoneNumber || '-',
      'Agenzia.Email': dati.email || dati.mail || '-',
      'Agenzia.Indirizzo.NomeStrada': dati.address || '-',
      'Agenzia.Indirizzo.Provincia': dati.provinceAcronym || '-',
      'Agenzia.Indirizzo.NumeroCivico': '-',
      'Agenzia.Indirizzo.Cap': dati.zipCode || '-',
      'Agenzia.Indirizzo.Comune': dati.town || '-',
      'Agenzia.CodiceAgenzia': dati.code || '-'
    };

    this._addFormGroupValue(dataToSend);
    this._callNextPage();
  }

  // Equivalente Angular handleSelezioneInstallatore()
  async handleSelezioneInstallatore(event) {
    const agency = event.detail;
    const dati = await this._arricchisciDati(agency);

    const dataToSend = {
      'GestioneProcesso.ContattiUniboxTemp.InstallatoreUnibox.NomeOfficina': dati.code_ext || '-',
      'GestioneProcesso.ContattiUniboxTemp.InstallatoreUnibox.TelefonoOfficina': dati.phoneNumber || '-',
      'GestioneProcesso.ContattiUniboxTemp.InstallatoreUnibox.EmailOfficina': dati.email || dati.mail || '-',
      'GestioneProcesso.ContattiUniboxTemp.InstallatoreUnibox.Indirizzo.NomeStrada': dati.address || '-',
      'GestioneProcesso.ContattiUniboxTemp.InstallatoreUnibox.Indirizzo.Provincia': dati.provinceAcronym || '-',
      'GestioneProcesso.ContattiUniboxTemp.InstallatoreUnibox.Indirizzo.NumeroCivico': '-',
      'GestioneProcesso.ContattiUniboxTemp.InstallatoreUnibox.Indirizzo.Cap': dati.zipCode || '-',
      'GestioneProcesso.ContattiUniboxTemp.InstallatoreUnibox.Indirizzo.Comune': dati.town || '-',
      'GestioneProcesso.ContattiUniboxTemp.InstallatoreUnibox.CodiceOfficina': dati.code || '-'
    };

    this._addFormGroupValue(dataToSend);
    this._callNextPage();
  }

  // Equivalente Angular _callNextPage()
  _callNextPage() {
    if (this._locatorAction) {
      // Equivalente Angular this.onChange()
      this.dispatchEvent(new CustomEvent('nextstep'));
    }
  }

  // Metodi per i mock buttons
  handleMockAgenzia() {
    this.handleSelezioneAgenzia({ detail: this.mockAgenzia });
  }

  handleMockInstallatore() {
    this.handleSelezioneInstallatore({ detail: this.mockInstallatore });
  }

  // Cleanup (equivalente Angular ngOnDestroy)
  disconnectedCallback() {
    if (this._lastDataToSend) {
      // Cleanup dei dati del form se necessario
    }
    // Equivalente Helpers.SessionHelper.deleteData('QUOTAZIONE_PEGA')
    sessionStorage.removeItem('QUOTAZIONE_PEGA');
  }
}
