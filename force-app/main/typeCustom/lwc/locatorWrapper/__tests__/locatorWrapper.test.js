import { createElement } from '@lwc/engine-dom';
import LocatorWrapper from 'c/locatorWrapper';

// Mock Google Maps API
global.google = {
    maps: {
        Map: jest.fn(),
        Marker: jest.fn(),
        InfoWindow: jest.fn(),
        MapTypeId: { ROADMAP: 'roadmap' },
        SymbolPath: { CIRCLE: 0 },
        Size: jest.fn()
    }
};

// Mock loadGoogleMapsApi
jest.mock('c/googleMapsLoader', () => ({
    loadGoogleMapsApi: jest.fn(() => Promise.resolve(true))
}));

describe('c-locator-wrapper', () => {
    afterEach(() => {
        // The jsdom instance is shared across test cases in a single file so reset the DOM
        while (document.body.firstChild) {
            document.body.removeChild(document.body.firstChild);
        }
    });

    it('should render component with mock data', () => {
        // Arrange
        const element = createElement('c-locator-wrapper', {
            is: LocatorWrapper
        });

        element.stepCorrente = 'scelta_agenzia';
        element.userLocation = {
            latitude: '44.991943',
            longitude: '9.010861',
            permission: 'granted'
        };

        // Act
        document.body.appendChild(element);

        // Assert
        const mapContainer = element.shadowRoot.querySelector('.map');
        expect(mapContainer).toBeTruthy();

        const mockButtons = element.shadowRoot.querySelectorAll('lightning-button');
        expect(mockButtons.length).toBeGreaterThan(0);
    });

    it('should handle agency selection', () => {
        // Arrange
        const element = createElement('c-locator-wrapper', {
            is: LocatorWrapper
        });

        const mockAgency = {
            code: '39680',
            management: 'Test Agency',
            latitude: '44.991943',
            longitude: '9.010861'
        };

        element.apiData = { poi: [mockAgency] };

        let selectedAgency = null;
        element.addEventListener('agencyselected', (event) => {
            selectedAgency = event.detail;
        });

        // Act
        document.body.appendChild(element);
        element.selectAgencyFromMap('39680');

        // Assert
        expect(selectedAgency).toEqual(mockAgency);
    });
});