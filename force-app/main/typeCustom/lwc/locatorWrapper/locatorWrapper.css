.locator-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  text-align: center;
}

.loading-container p {
  margin-top: 16px;
  color: #666;
  font-size: 14px;
}

.map-section {
  flex: 1;
  min-height: 400px;
  position: relative;
}

.map-container {
  width: 100%;
  height: 100%;
  min-height: 400px;
}

.agencies-section {
  background: white;
  border-top: 1px solid #e0e0e0;
  max-height: 300px;
  overflow-y: auto;
  padding: 16px;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: bold;
  color: #183a56;
}

.agencies-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.agency-item {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.agency-icon {
  width: 40px;
  height: 40px;
  margin-right: 16px;
  flex-shrink: 0;
}

.agency-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.agency-info {
  flex: 1;
}

.agency-name {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: bold;
  color: #183a56;
}

.agency-details {
  margin-bottom: 12px;
}

.agency-distance {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: #c4151c;
}

.agency-address,
.agency-contact {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: #666;
}

.agency-actions {
  display: flex;
  justify-content: flex-end;
}

/* Responsive design */
@media (min-width: 768px) {
  .locator-container {
    flex-direction: row;
  }

  .map-section {
    flex: 2;
  }

  .agencies-section {
    flex: 1;
    max-width: 400px;
    max-height: none;
    border-top: none;
    border-left: 1px solid #e0e0e0;
  }
}

@media (max-width: 767px) {
  .map-section {
    min-height: 300px;
  }

  .agencies-section {
    max-height: 250px;
  }
}