import { loadGoogleMapsApi } from 'c/googleMapsLoader';
import BffInterprete from 'c/bffInterprete';
import { LightningElement, api, track } from 'lwc';

export default class LocatorWrapper extends LightningElement {
  @api stepCorrente;
  @track mapMarkers = [];
  @api userLocation;
  @api apiData;

  _bffInterprete = new BffInterprete(this.config);
  buttonFindAgency = {
    validationMessages: '',
    visible: true,
    labelReserveSpace: false,
    readOnly: false,
    control: {
      modes: [
        {
          tooltip: '',
          modeType: 'ignore',
        },
        {
          tooltip: '',
          modeType: 'readOnly',
          autoPrepend: '',
          autoAppend: '',
          controlFormat: 'button seleziona agenzia',
          formatType: 'text',
          showReadOnlyValidation: 'false',
        },
      ],
      actionSets: [
        {
          actions: [
            {
              action: 'runDataTransform',
            },
            {
              action: 'finishAssignment',
            },
          ],
          events: [
            {
              event: 'click',
            },
          ],
        },
      ],
      type: 'pxButton',
      label: 'Seleziona Agenzia',
    },
    label: 'Button',
    type: 'Text',
    required: false,
    validateAs: '',
    reference: 'pyTemplateButton',
    labelFormat: 'Standard',
    disabled: false,
    testID: '202303021648040404700',
    value: '',
    maxLength: 0,
    expectedLength: '',
    fieldID: 'pyTemplateButton',
    customAttributes: {
      'GestioneProcesso.StepSuccessivo': 'MODIFICA TARGA',
    },
    showLabel: false,
  };

  addressAutocompleteTest = {
    label: '',
    control: {
      modes: [{ placeholder: 'Città, Indirizzo, CAP' }],
      options: [],
    },
    readOnly: false,
    labelFormat: 'Text APP GDB16 WEB BAB16 BAB16 BAB16',
    disabled: false,
    value: '',
    reference: '',
  };

  googleMapsInitialized = false;
  map;

  // Google Maps API Key - using the working key from addressAutocomplete
  GOOGLE_API_KEY = 'AIzaSyCOyfBmSpLkC7mpvBN0gfu20R-pANS2tMU';

  get isAgencyStep() {
    return this.stepCorrente === 'scelta_agenzia';
  }

  get isInstallerStep() {
    return this.stepCorrente === 'scelta_installatore';
  }

  renderedCallback() {
    if (this.googleMapsInitialized) return;
    this.googleMapsInitialized = true;

    if (!this.apiData)
      this.apiData = {
        operationResult: {
          type: '0',
          code: '00',
          message: '',
        },
        poi: [
          {
            delegates: '',
            distance: '0,4',
            closing: '',
            zipCode: '27058',
            phoneNumber: '*********',
            patronalFeast: '',
            divestitureDest: '',
            finitaliaMandate: '0',
            management: "SERVIZI ASSICURATIVI OLTREPO' SRL",
            code: '39680',
            divisionCode: '000000484',
            longitude: '9.010861',
            latitude: '44.991943',
            openingTime:
              '||09:00|13:00|15:00|19:00|09:00|13:00|15:00|19:00|09:00|13:00|15:00|19:00|09:00|13:00|15:00|19:00|09:00|13:00|15:00|19:00|09:00|12:30|CHIUSO|CHIUSO|',
            town: 'VOGHERA',
            suspensionDate: '',
            provinceAcronym: 'PV',
            faxNumber: '*********',
            address: 'VIA GIUSEPPE GARIBALDI 37',
            itemType: 'AGE',
            provinceCode: '018',
            brands: '',
            newManagementCode: '',
            email: '<EMAIL>',
            name: 'VOGHERA',
            treatedBranches: 'X|X|X|X|X|X',
            locationIstatCode: '018182',
            VatTaxCode: '02854400187',
          },
        ],
      };

    if (this.userLocation?.permission === 'granted') {
      this.loadGoogleMaps();
    }
  }

  async loadGoogleMaps() {
    try {
      const success = await loadGoogleMapsApi(this.GOOGLE_API_KEY);
      if (success) {
        this.initMap();
      } else {
        console.error('Failed to load Google Maps API');
      }
    } catch (error) {
      console.error('Error loading Google Maps:', error);
    }
  }



  initMap() {
    const mapContainer = this.template.querySelector('.map');

    // Initialize Google Map
    this.map = new google.maps.Map(mapContainer, {
      center: {
        lat: parseFloat(this.userLocation.latitude),
        lng: parseFloat(this.userLocation.longitude)
      },
      zoom: 15,
      mapTypeId: google.maps.MapTypeId.ROADMAP,
    });

    // Add user location marker
    new google.maps.Marker({
      position: {
        lat: parseFloat(this.userLocation.latitude),
        lng: parseFloat(this.userLocation.longitude)
      },
      map: this.map,
      title: 'La tua posizione',
      icon: {
        path: google.maps.SymbolPath.CIRCLE,
        scale: 8,
        fillColor: '#66cbf1',
        fillOpacity: 1,
        strokeColor: 'white',
        strokeWeight: 2,
      },
    });

    // Add agency markers
    this.apiData?.poi.forEach((agency) => {
      if (agency.latitude && agency.longitude) {
        const marker = new google.maps.Marker({
          position: {
            lat: parseFloat(agency.latitude),
            lng: parseFloat(agency.longitude)
          },
          map: this.map,
          title: `Agenzia ${agency.code} - ${agency.management}`,
          icon: {
            url: 'https://unipolsai.it/UnipolSaiThemeDynamicWAR/themes/html/dynamicSpots/assets/images/locator/Risorsa4.png',
            scaledSize: new google.maps.Size(30, 30),
          },
        });

        // Create info window
        const infoWindow = new google.maps.InfoWindow({
          content: this.createInfoWindowContent(agency),
        });

        // Add click listener to marker
        marker.addListener('click', () => {
          infoWindow.open(this.map, marker);
        });
      }
    });
  }

  createInfoWindowContent(agency) {
    let openingTime = agency.openingTime.split('|').filter((time) => time.trim());
    const days = ['Lun', 'Mar', 'Mer', 'Gio', 'Ven', 'Sab', 'Dom'];
    let openingTimeComponent = '';

    for (let i = 0; i < days.length; i++) {
      const offset = i * 4;
      if (openingTime.length <= offset) break;

      const m1 = openingTime[offset];
      const m2 = openingTime[offset + 1];
      const e1 = openingTime[offset + 2];
      const e2 = openingTime[offset + 3];

      const isClosedAllDay = [m1, m2, e1, e2].every((time) => time === 'CHIUSO');
      if (isClosedAllDay) continue;

      const morning = m1 !== 'CHIUSO' && m2 !== 'CHIUSO' ? `${m1} - ${m2}` : null;
      const evening = e1 !== 'CHIUSO' && e2 !== 'CHIUSO' ? `${e1} - ${e2}` : null;

      openingTimeComponent += `<div class="tpd_openingTime">
                                <span class="tdp_openingTime_day">${days[i]}&nbsp; </span>
                                ${morning ? `<span class="tdp_openingTime_morning">${morning}</span>` : ''}
                                ${morning && evening ? `<span>/</span>` : '<span class="tdp_openingTime_evening">FILIALE CHIUSA</span>'}
                                ${evening ? `<span class="tdp_openingTime_evening">${evening}</span>` : ''}
                              </div>`;
    }

    return `<div class="locator-info-container">
              <div class="tpd-icons">
                <img src="/UnipolSaiThemeDynamicWAR/themes/html/dynamicSpots/assets/images/locator/Risorsa4.png" alt="" class="tpd-icon">
              </div>
              <div class="locator-info-data">
                <div class="tpd_agency_name">Agenzia ${agency.code} - Agenzia di ${agency.management} </div>
                <div class="tpd_bold">${agency.distance} km</div>
                <div class="tpd_agency_address tpd_normal">
                  ${agency.address} ${agency.code}, ${agency.zipCode}, ${agency.name}
                </div>
                <div class="tpd_agency_contacts tpd_normal">
                  <span class="tpd_agency_phoneNumber">Tel:</span> ${agency.phoneNumber}
                  <span class="tpd_agency_faxNumber"> - Fax: </span> ${agency.faxNumber}
                </div>
                <div class="tpd_agency_email tpd_normal">
                  <a href="mailto:${agency.email}" tabindex="0">
                    ${agency.email}
                  </a>
                </div>
                <div class="tpd_openingTime_wrapper">
                  ${openingTimeComponent}
                </div>
                <div class="tpd_cta_agency" style="text-align: center;">
                  <button class="button-seleziona-agenzia" onclick="window.selectAgency('${agency.code}')">Seleziona Agenzia</button>
                </div>
              </div>
            </div>`;
  }

  moveToAgency(evt) {
    const { latitude, longitude } = evt.currentTarget.dataset;
    if (this.map && latitude && longitude) {
      this.map.panTo({
        lat: parseFloat(latitude),
        lng: parseFloat(longitude)
      });
      this.map.setZoom(16);
    }
  }

  simulateComponentReady() {
    this.dispatchEvent(new CustomEvent('componentready'));
  }

  selectMockAgency() {
    const mock = {
      name: 'Agenzia Mario Rossi',
      code: 'AG001',
      phoneNumber: '0123456789',
      email: '<EMAIL>',
      address: 'Via Roma 1',
      zipCode: '00100',
      town: 'Roma',
      provinceAcronym: 'RM',
    };
    this.dispatchEvent(new CustomEvent('agencyselected', { detail: mock }));
  }

  selectMockInstaller() {
    const mock = {
      code_ext: 'OFF123',
      phoneNumber: '0987654321',
      email: '<EMAIL>',
      address: 'Via Meccanici 10',
      zipCode: '20100',
      town: 'Milano',
      provinceAcronym: 'MI',
    };
    this.dispatchEvent(new CustomEvent('installerselected', { detail: mock }));
  }

  // Method to handle agency selection from InfoWindow
  selectAgencyFromMap(agencyCode) {
    const agency = this.apiData?.poi.find(poi => poi.code === agencyCode);
    if (agency) {
      this.dispatchEvent(new CustomEvent('agencyselected', { detail: agency }));
    }
  }

  // Expose method to global scope for InfoWindow button clicks
  connectedCallback() {
    window.selectAgency = (agencyCode) => {
      this.selectAgencyFromMap(agencyCode);
    };
  }

  disconnectedCallback() {
    if (window.selectAgency) {
      delete window.selectAgency;
    }
  }
}
