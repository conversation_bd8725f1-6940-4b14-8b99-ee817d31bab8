import { LightningElement, track } from 'lwc';


export default class LocatorWrapper extends LightningElement {
  // // Proprietà API (equivalenti Angular @Input)
  // @api stepCorrente; // 'scelta_agenzia' | 'scelta_installatore'

  // // Proprietà interne (equivalenti _staticData Angular)
  // @track isConfiguratore = true;
  // @track showFilters = false;
  // @track selectedAddress = '';
  // @track showInfoWindow = true;
  // @track isProdottoUnico = true;

  // // Dati e stato
  // @track userLocation = null;
  // @track agenciesList = [];
  // @track allAgencies = [];
  // @track isLoading = true;
  // @track mapInitialized = false;

  // // Google Maps
  // map = null;
  // GOOGLE_API_KEY = 'AIzaSyCOyfBmSpLkC7mpvBN0gfu20R-pANS2tMU';

  // // Servizi
  // _bffInterprete;

  // // Getter per determinare il tipo di step
  // get isAgencyStep() {
  //   return this.stepCorrente === 'scelta_agenzia';
  // }

  // get isInstallerStep() {
  //   return this.stepCorrente === 'scelta_installatore';
  // }

  // connectedCallback() {
  //   console.log('LocatorWrapper connected, step:', this.stepCorrente);

  //   // Inizializza BffInterprete con configurazione corretta
  //   // Usa la stessa configurazione dell'interprete principale
  //   const config = {
  //     productType: 'PUVEICOLO', // o il valore corretto
  //     env: 'UNICO'
  //   };
  //   this._bffInterprete = new BffInterprete(config);

  //   // Esponi metodo globale per InfoWindow
  //   window.selectAgencyFromMap = (agencyCode) => {
  //     this.selectAgencyFromMap(agencyCode);
  //   };

  //   this._initialize();
  // }

  // async _initialize() {
  //   try {
  //     // 1. Ottieni posizione utente
  //     this.userLocation = await this._getUserLocation();
  //     console.log('User location obtained:', this.userLocation);

  //     // 2. Carica dati agenzie
  //     await this._loadAgenciesData();

  //     // 3. Carica Google Maps
  //     await this._loadGoogleMaps();

  //     // 4. Notifica caricamento completato
  //     this._notifyComponentReady();
  //   } catch (error) {
  //     console.error('Error initializing LocatorWrapper:', error);
  //     this.isLoading = false;
  //   }
  // }

  // // Metodo per ottenere la posizione utente
  // async _getUserLocation() {
  //   return new Promise((resolve) => {
  //     if (!navigator.geolocation) {
  //       console.warn('Geolocation not supported');
  //       // Usa posizione mock per Voghera (come nei dati di test)
  //       resolve({
  //         latitude: 44.991943,
  //         longitude: 9.010861,
  //         permission: 'mock'
  //       });
  //       return;
  //     }

  //     navigator.geolocation.getCurrentPosition(
  //       (position) => {
  //         resolve({
  //           latitude: position.coords.latitude,
  //           longitude: position.coords.longitude,
  //           permission: 'granted'
  //         });
  //       },
  //       (error) => {
  //         console.warn('Geolocation error:', error);
  //         // Usa posizione mock in caso di errore
  //         resolve({
  //           latitude: 44.991943,
  //           longitude: 9.010861,
  //           permission: 'denied'
  //         });
  //       },
  //       { timeout: 10000, enableHighAccuracy: true }
  //     );
  //   });
  // }

  // // Metodo per caricare i dati delle agenzie
  // async _loadAgenciesData() {
  //   if (!this.userLocation) return;

  //   try {
  //     const urls = this._bffInterprete.getURLS();
  //     const headers = this._bffInterprete.getCustomHeaders();

  //     // Carica agenzie vicine
  //     const closeAgenciesUrl = urls.getCloseAgencies(
  //       this.userLocation.latitude,
  //       this.userLocation.longitude
  //     );
  //     console.log('Loading close agencies from:', closeAgenciesUrl);

  //     const closeAgencies = await utils.apiRequest('GET', closeAgenciesUrl, headers);
  //     this.agenciesList = closeAgencies ? closeAgencies.slice(0, 10) : [];

  //     // Carica tutte le agenzie
  //     const allAgenciesUrl = urls.getAllAgencies();
  //     console.log('Loading all agencies from:', allAgenciesUrl);

  //     const allAgencies = await utils.apiRequest('GET', allAgenciesUrl, headers);
  //     this.allAgencies = allAgencies ? allAgencies.slice(0, 50) : [];

  //     console.log('Agencies loaded - Close:', this.agenciesList.length, 'All:', this.allAgencies.length);
  //   } catch (error) {
  //     console.error('Error loading agencies data:', error);
  //     // Usa dati mock in caso di errore
  //     this._loadMockData();
  //   }
  // }

  // // Carica dati mock per testing
  // _loadMockData() {
  //   this.agenciesList = [
  //     {
  //       code: '39680',
  //       management: "SERVIZI ASSICURATIVI OLTREPO' SRL",
  //       name: 'VOGHERA',
  //       phoneNumber: '038340134',
  //       email: '<EMAIL>',
  //       address: 'VIA GIUSEPPE GARIBALDI 37',
  //       zipCode: '27058',
  //       town: 'VOGHERA',
  //       provinceAcronym: 'PV',
  //       latitude: '44.991943',
  //       longitude: '9.010861',
  //       distance: '0.4'
  //     },
  //     {
  //       code: '48123',
  //       management: 'ASSICURA SNC',
  //       name: 'PAVIA',
  //       phoneNumber: '0382 987654',
  //       address: 'Corso Cavour, 45',
  //       zipCode: '27100',
  //       town: 'Pavia',
  //       distance: 25.4
  //     },
  //     {
  //       code: '51233',
  //       management: 'LIGURIA ASSICURA SRL',
  //       name: 'GENOVA',
  //       phoneNumber: '010 334455',
  //       address: 'Via XX Settembre, 100',
  //       zipCode: '16121',
  //       town: 'Genova',
  //       distance: 80.0
  //     }
  //   ];
  //   this.allAgencies = [...this.agenciesList];

  //   this.isLoading = false;
  //   console.log('Mock data loaded: ', this.agenciesList);
  // }

  // // Metodo per caricare Google Maps
  // async _loadGoogleMaps() {
  //   try {
  //     console.log('Loading Google Maps API directly...');

  //     // Controlla se Google Maps è già caricato
  //     if (window.google && window.google.maps) {
  //       console.log('Google Maps already loaded');
  //       this._initializeMap();
  //       return;
  //     }

  //     // Carica Google Maps API direttamente
  //     const script = document.createElement('script');
  //     script.src = `https://maps.googleapis.com/maps/api/js?key=${this.GOOGLE_API_KEY}&libraries=places`;
  //     script.async = true;
  //     script.defer = true;

  //     script.onload = () => {
  //       console.log('Google Maps API loaded successfully');
  //       this._initializeMap();
  //     };

  //     script.onerror = (error) => {
  //       console.error('Failed to load Google Maps API:', error);
  //       // Fallback: usa dati mock senza mappa
  //       this._notifyComponentReady();
  //     };

  //     document.head.appendChild(script);

  //   } catch (error) {
  //     console.error('Error loading Google Maps:', error);
  //     // Fallback: usa dati mock senza mappa
  //     this._notifyComponentReady();
  //   }
  // }

  // // Inizializza la mappa Google Maps
  // _initializeMap() {
  //   console.log('Initializing Google Map...');

  //   // Verifica che Google Maps sia disponibile
  //   if (!window.google || !window.google.maps) {
  //     console.error('Google Maps API not available');
  //     this._notifyComponentReady();
  //     return;
  //   }

  //   const mapContainer = this.template.querySelector('.map-container');
  //   console.log('Map container found:', !!mapContainer);
  //   console.log('mapcontainer value', mapContainer === null);

  //   if (!mapContainer || !this.userLocation) {
  //     console.error('Map container not found or no user location');
  //     this._notifyComponentReady();
  //     return;
  //   }

  //   console.log('Creating Google Map instance...');
  //   this.map = new google.maps.Map(mapContainer, {
  //     center: {
  //       lat: parseFloat(this.userLocation.latitude),
  //       lng: parseFloat(this.userLocation.longitude)
  //     },
  //     zoom: 12,
  //     mapTypeId: google.maps.MapTypeId.ROADMAP
  //   });

  //   // Aggiungi marker per posizione utente
  //   this._addUserLocationMarker();

  //   // Aggiungi marker per agenzie
  //   this._addAgencyMarkers();

  //   this.mapInitialized = true;
  //   console.log('Google Map initialized successfully');
  // }

  // // Aggiungi marker per posizione utente
  // _addUserLocationMarker() {
  //   new google.maps.Marker({
  //     position: {
  //       lat: parseFloat(this.userLocation.latitude),
  //       lng: parseFloat(this.userLocation.longitude)
  //     },
  //     map: this.map,
  //     title: 'La tua posizione',
  //     icon: {
  //       path: google.maps.SymbolPath.CIRCLE,
  //       scale: 8,
  //       fillColor: '#4285F4',
  //       fillOpacity: 1,
  //       strokeColor: '#ffffff',
  //       strokeWeight: 2
  //     }
  //   });
  // }

  // // Aggiungi marker per agenzie
  // _addAgencyMarkers() {
  //   const agencies = this.isAgencyStep ? this.agenciesList : this.allAgencies;

  //   agencies.forEach((agency) => {
  //     if (agency.latitude && agency.longitude) {
  //       const marker = new google.maps.Marker({
  //         position: {
  //           lat: parseFloat(agency.latitude),
  //           lng: parseFloat(agency.longitude)
  //         },
  //         map: this.map,
  //         title: `${agency.code} - ${agency.management || agency.name}`,
  //         icon: {
  //           url: '/UnipolSaiThemeDynamicWAR/themes/html/dynamicSpots/assets/images/locator/Risorsa4.png',
  //           scaledSize: new google.maps.Size(30, 30)
  //         }
  //       });

  //       // Aggiungi InfoWindow
  //       const infoWindow = new google.maps.InfoWindow({
  //         content: this._createInfoWindowContent(agency)
  //       });

  //       marker.addListener('click', () => {
  //         infoWindow.open(this.map, marker);
  //       });
  //     }
  //   });
  // }

  // // Crea contenuto InfoWindow
  // _createInfoWindowContent(agency) {
  //   return `
  //     <div style="max-width: 300px;">
  //       <h3>${agency.code} - ${agency.management || agency.name}</h3>
  //       <p><strong>Indirizzo:</strong> ${agency.address}, ${agency.zipCode} ${agency.town}</p>
  //       <p><strong>Telefono:</strong> ${agency.phoneNumber}</p>
  //       <p><strong>Email:</strong> ${agency.email}</p>
  //       ${agency.distance ? `<p><strong>Distanza:</strong> ${agency.distance} km</p>` : ''}
  //       <button onclick="window.selectAgencyFromMap('${agency.code}')"
  //               style="background: #c4151c; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
  //         Seleziona ${this.isAgencyStep ? 'Agenzia' : 'Installatore'}
  //       </button>
  //     </div>
  //   `;
  // }

  // // Notifica caricamento completato (equivalente Angular onLoadData)
  // _notifyComponentReady() {
  //   this.isLoading = false;
  //   this.dispatchEvent(new CustomEvent('componentready'));
  // }

  // // Gestione selezione agenzia dalla mappa
  // selectAgencyFromMap(agencyCode) {
  //   const agencies = this.isAgencyStep ? this.agenciesList : this.allAgencies;
  //   const agency = agencies.find(a => a.code === agencyCode);

  //   if (agency) {
  //     if (this.isAgencyStep) {
  //       this.dispatchEvent(new CustomEvent('agencyselected', { detail: agency }));
  //     } else {
  //       this.dispatchEvent(new CustomEvent('installerselected', { detail: agency }));
  //     }
  //   }
  // }



  // // Gestione selezione agenzia dalla lista
  // handleAgencySelection(event) {
  //   const agencyCode = event.target.dataset.agencyCode;
  //   this.selectAgencyFromMap(agencyCode);
  // }

  // disconnectedCallback() {
  //   if (window.selectAgencyFromMap) {
  //     delete window.selectAgencyFromMap;
  //   }
  // }
  @track selectedAgency = '';x
  @track mapCenter = {
      location: {
          Latitude: 41.8719,  // Centro Italia
          Longitude: 12.5674
      }
  };

  agencies = {
      roma: { Latitude: 41.9028, Longitude: 12.4964, Name: 'Agenzia Roma' },
      milano: { Latitude: 45.4642, Longitude: 9.19, Name: 'Agenzia Milano' },
      napoli: { Latitude: 40.8518, Longitude: 14.2681, Name: 'Agenzia Napoli' }
  };

  get agencyOptions() {
      return [
          { label: '-- Seleziona --', value: '' },
          { label: 'Agenzia Roma', value: 'roma' },
          { label: 'Agenzia Milano', value: 'milano' },
          { label: 'Agenzia Napoli', value: 'napoli' }
      ];
  }

  get mapMarkers() {
      if (this.selectedAgency && this.agencies[this.selectedAgency]) {
          const agency = this.agencies[this.selectedAgency];
          return [
              {
                  location: {
                      Latitude: agency.Latitude,
                      Longitude: agency.Longitude
                  },
                  title: agency.Name
              }
          ];
      }
      return [];
  }

  handleAgencyChange(event) {
      this.selectedAgency = event.detail.value;
      if (this.selectedAgency && this.agencies[this.selectedAgency]) {
          const agency = this.agencies[this.selectedAgency];
          this.mapCenter = {
              location: {
                  Latitude: agency.Latitude,
                  Longitude: agency.Longitude
              }
          };
      } else {
          this.mapCenter = {
              location: {
                  Latitude: 41.8719,
                  Longitude: 12.5674
              }
          };
      }
  }
}
