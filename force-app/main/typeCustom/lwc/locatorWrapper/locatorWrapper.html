<template>
  <div class="locator-container">
    <!-- Loading indicator -->
    <template if:true={isLoading}>
      <div class="loading-container">
        <lightning-spinner alternative-text="Caricamento..." size="medium"></lightning-spinner>
        <p>Caricamento dati agenzie...</p>
      </div>
    </template>

    <!-- Main content -->
    <template if:false={isLoading}>
      <!-- Map container -->
      <div class="map-section">
        <div class="map-container" lwc:dom="manual"></div>
      </div>

      <!-- Agencies list -->
      <div class="agencies-section">
        <template if:true={isAgencyStep}>
          <h3 class="section-title">Agenzie vicine</h3>
        </template>
        <template if:false={isAgencyStep}>
          <h3 class="section-title">Installatori vicini</h3>
        </template>

        <div class="agencies-list">
          <template for:each={agenciesList} for:item="agency">
            <div key={agency.code} class="agency-item">
              <div class="agency-icon">
                <img src="/UnipolSaiThemeDynamicWAR/themes/html/dynamicSpots/assets/images/locator/Risorsa2.png"
                     alt="Agency icon" />
              </div>

              <div class="agency-info">
                <h4 class="agency-name">
                  {agency.code} - {agency.management}
                </h4>

                <div class="agency-details">
                  <p class="agency-distance">
                    <strong>{agency.distance} km</strong>
                  </p>
                  <p class="agency-address">
                    {agency.address}, {agency.zipCode} {agency.town}
                  </p>
                  <p class="agency-contact">
                    Tel: {agency.phoneNumber}
                  </p>
                </div>

                <div class="agency-actions">
                  <template if:true={isAgencyStep}>
                    <lightning-button
                      label="Seleziona Agenzia"
                      variant="brand"
                      data-agency-code={agency.code}
                      onclick={handleAgencySelection}>
                    </lightning-button>
                  </template>
                  <template if:false={isAgencyStep}>
                    <lightning-button
                      label="Seleziona Installatore"
                      variant="brand"
                      data-agency-code={agency.code}
                      onclick={handleAgencySelection}>
                    </lightning-button>
                  </template>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
    </template>
  </div>
</template>