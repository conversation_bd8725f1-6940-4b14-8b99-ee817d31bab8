<template>
  <!-- <div class="locator-container">
    <template if:true={isLoading}>
      <div class="loading-container">
        <lightning-spinner alternative-text="Caricamento..." size="medium"></lightning-spinner>
        <p>Caricamento dati agenzie...</p>
      </div>
    </template>

    <template if:false={isLoading}>
      <div class="map" style="height: 400px;">
        <div class="map-container" lwc:dom="manual"></div>
      </div>

      <div class="agencies-section">
        <template if:true={isAgencyStep}>
          <h3 class="section-title">Age<PERSON>ie vicine</h3>
        </template>
        <template if:false={isAgencyStep}>
          <h3 class="section-title">Installatori vicini</h3>
        </template>

        <div class="agencies-list">
          <template for:each={agenciesList} for:item="agency">
            <div key={agency.code} class="agency-item">
              <div class="agency-icon">
                <img src="/UnipolSaiThemeDynamicWAR/themes/html/dynamicSpots/assets/images/locator/Risorsa2.png"
                     alt="Agency icon" />
              </div>

              <div class="agency-info">
                <h4 class="agency-name">
                  {agency.code} - {agency.management}
                </h4>

                <div class="agency-details">
                  <p class="agency-distance">
                    <strong>{agency.distance} km</strong>
                  </p>
                  <p class="agency-address">
                    {agency.address}, {agency.zipCode} {agency.town}
                  </p>
                  <p class="agency-contact">
                    Tel: {agency.phoneNumber}
                  </p>
                </div>

                <div class="agency-actions">
                  <template if:true={isAgencyStep}>
                    <lightning-button
                      label="Seleziona Agenzia"
                      variant="brand"
                      data-agency-code={agency.code}
                      onclick={handleAgencySelection}>
                    </lightning-button>
                  </template>
                  <template if:false={isAgencyStep}>
                    <lightning-button
                      label="Seleziona Installatore"
                      variant="brand"
                      data-agency-code={agency.code}
                      onclick={handleAgencySelection}>
                    </lightning-button>
                  </template>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
    </template>
  </div> -->

  <lightning-card title="Selezione Agenzie">
    <div class="slds-p-around_medium">
        <lightning-combobox
            name="agencySelect"
            label="Seleziona Agenzia"
            placeholder="-- Seleziona --"
            options={agencyOptions}
            value={selectedAgency}
            onchange={handleAgencyChange}>
        </lightning-combobox>

        <c-address-autocomplete></c-address-autocomplete>



        <lightning-map
            map-markers={mapMarkers}
            zoom-level="10"
            center={mapCenter}
            class="map">
        </lightning-map>
    </div>
</lightning-card>

</template>