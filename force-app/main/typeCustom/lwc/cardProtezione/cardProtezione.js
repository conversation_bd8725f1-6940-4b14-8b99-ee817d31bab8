import { utils } from 'c/utils';
import { LightningElement, api } from 'lwc';

export default class CardProtezione extends LightningElement {
  _groups;

  @api
  get groups() {
    return this._groups;
  }

  set groups(value) {
    this._groups = value;
    this.initializeData();
  }

  get nomeGaranzia() {
    const caption = utils.getFirstCaptionByControlFormat(this._groups, 'NomeGaranzia');
    return utils.captionToValue(caption)?.value || '';
  }

  get descrizioneGaranzia() {
    const caption = utils.getFirstCaptionByControlFormat(this._groups, 'DescrizioneGaranzia');
    return utils.captionToValue(caption)?.value || '';
  }

  get testoPrezzoGaranzia() {
    const caption = utils.getFirstCaptionByControlFormat(this._groups, 'TestoPrezzoGaranzia');
    return utils.captionToValue(caption)?.value || '';
  }

  get attributiGaranzia() {
    const customComponents = utils.getAllLayoutInGroupsByGroupFormat(this._groups, 'CustomComponent');
    // Array finale dei risultati
    let attributi = [];

    // Ciclo su ogni customComponent
    customComponents.forEach((customComponent) => {
      const fields = utils.getEachVisibleFieldInGroups(customComponent.groups);
      
      // Prendo tutti i pxTextInput
      const valueFields = fields.filter((f) => f.control?.type === 'pxTextInput');

      

      // Per ogni pxTextInput creo l'oggetto da restituire
      valueFields.forEach((field) => {
        const rawValue = field?.value || '';
        const formattedValue = this.formattaTesto(rawValue);
        // Prendo la label una sola volta per componente
        const labelValue = utils.captionToValue(
          utils.getFirstCaptionByControlFormat(customComponent.groups, 'ChiaveAttributoGaranzia')
        ).value;
        attributi.push({
          label: labelValue,
          value: formattedValue,
          labelValueString: labelValue && formattedValue
            ? `${labelValue}: ${formattedValue}`
            : labelValue || formattedValue || null
        });
      });
    });

    return attributi;
  }

  get premioRataScontato() {
    const result = utils.getFirstCurrencyByFieldId(this._groups, 'RataPremioLordoScontato');
    if (result) {
      return `${result.value}${result.symbol}`;
    }
  }

  get icon() {
    return utils.getFirstFieldInGroupsByType(this._groups, 'pxIcon');
  }

  get checkbox() {
    return utils.getFirstFieldInGroupsByType(this._groups, 'pxCheckbox');
  }

  formattaTesto(testo) {
    return utils.decodeHTML(testo);
  }

  formattaValuta(valuta) {
    return utils.formatCurrency(valuta);
  }

  get premioLordo() {
    const premioLordoField = utils.fieldToValue(
      utils.getFirstFieldInGroupsByType(this._groups, 'pxCurrency')
    );

    return this.formattaValuta(premioLordoField);
  }

  get occorrenzaPrezzoGaranzia() {
    return utils.captionToValue(utils.getFirstCaptionByControlFormat(this._groups, 'TestoPrezzoGaranzia'))
      .value;
  }

  initializeData() {
    if (this._field?.customAttributes) {
      this.assurancePackageHeader = this._field.customAttributes.assurancePackageHeader;
    }

    if (this.checkbox) {
      this.checked = this.checkbox.value === 'true';
    }
  }
}