import { LightningElement, api, track } from 'lwc';
import { utils } from 'c/utils';
import { addressAutocompleteService } from './addressAutocompleteService';
import getPlacesAutocomplete from '@salesforce/apex/GoogleMapsProxy.getPlacesAutocomplete';
import getPlacesDetails from '@salesforce/apex/GoogleMapsProxy.getPlaceDetails';
import { fireEvent } from 'c/pubsub';
import BffInterprete from 'c/bffInterprete';
import { getFormState  } from 'c/formState';


const AddresModeType = {
  MANUALE: 'MANUALE',
  INDIRIZZO: 'INDIRIZZO',
  AUTOCOMPLETAMENTO: 'AUTOCOMPLETAMENTO'
};

// const GOOGLE_API_KEY = 'AIzaSyBOti4mM-6x9WDnZIjIeyEU21OpBXqWBgw';
export default class AddressAutocomplete extends LightningElement {
  _currentAddressInfo = {};
  _referenceBoxManuale = {};

  _field;
  _groups;

  @track currentState = AddresModeType.AUTOCOMPLETAMENTO;
  @track errorStatus = false;
  @track errorMessage = '';
  @track inputPrincipaleUtente = '';
  @track inputSecondarioUtente = '';
  @track processoDiInvioDati = false;
  @track showDropdown = false;
  @track predictions = [];
  @track isSearchingPredictions = false;
  @track buttonConfermaIndirizzoManuale;
  @track buttonAnnullaInserimentoManuale;


  @api blockSubmit = false;
  @api tipoAutocomplete = 'residenza';


  @api
  set field(value) {
    this._field = value;
    this._init();
  }
  get field() { return this._field; }

  @api
  set groups(value) {
    this._groups = value;
    this._init();
  }
  get groups() { return this._groups; }

  setCurrentState(state) {
    this.currentState = state;
    localStorage.setItem('currentStateAddressAutocomplete', state);
  }

  get isManualState() {
    return this.currentState === AddresModeType.MANUALE;
  }

  get isIndirizzoState() {
    return this.currentState === AddresModeType.INDIRIZZO;
  }

  get notIndirizzoState() {
    return this.currentState !== AddresModeType.INDIRIZZO;
  }

  get isRequired() {
    return this.field && this.field.customAttributes && this.field.customAttributes.required === 'true';
  }
  
  get showSubmitBlocker() {
    const isRequiredAndEmptyForManual = this.isRequired && this.currentState === AddresModeType.MANUALE && this._fullData.length === 0;
    const shouldBlockForIndirizzoState = this.isRequired && this.currentState === AddresModeType.INDIRIZZO;
    const shouldBlock = this.blockSubmit || isRequiredAndEmptyForManual || shouldBlockForIndirizzoState;
    return shouldBlock;
  }

  get _fullData() {
    let esito = "";
    for (const key in this._currentAddressInfo) {
      esito += this._currentAddressInfo[key] || "";
    }
    return esito.trim();
  }

  get labelAutocomplete() {
    return this._field?.customAttributes?.labelAutocomplete || '';
  }

  get stileLabels() {
    return this._field?.customAttributes?.styleLabelAutocomplete || this._field?.customAttributes?.stileLabels || '';
  }

  get placeholder() {
    return this._field?.customAttributes?.placeholder || 'Via Roma 21, Milano 20156';
  }

  get inputPrincipaleClass() {
    return `input-dati-utente ${this.errorStatus ? 'error' : ''}`;
  }

  get inputSecondarioClass() {
    return `input-dati-utente ${this.errorStatus ? 'error' : ''}`;
  }

  get IndirizzoCaption() {
    return this._field?.customAttributes?.IndirizzoCaption || '';
  }

  get showCambioInserimento() {
    return !!(this.nonTroviIlTuoIndirizzoLabel && this.nonTroviIlTuoIndirizzoCta && !this.isManualState);
  }

  get showErrorMessage() {
    return this.errorStatus && this.errorMessage?.length > 0;
  }

  get showInserimentoManuale() {
    return !!(this.viewInserimentoManuale && this.isManualState);
  }

  get showMainInput() {
    return !this.isManualState;
  }

  get nonTroviIlTuoIndirizzoLabel() {
    return this._field?.customAttributes?.nonTroviIlTuoIndirizzoLabel || 'Non trovi il tuo indirizzo?';
  }

  get showButtonConfermaInserimentoCivico() {
    const result = !!(this.buttonConfermaInserimentoCivico && this.isIndirizzoState);
    return result;
  }

  get showNonTroviIlTuoIndirizzo() {
    const result = !!(this.nonTroviIlTuoIndirizzoCta && !this.isManualState);
    return result;
  }

  get predictionsCount() {
    return this.predictions.length;
  }

  get shouldShowDropdown() {
    return this.showDropdown && this.predictions.length > 0;
  }

  get usingMockData() {
    return false;
  }

  get fieldValue() {
    return this._getFieldValue();
  }

  get buttonConfermaInserimentoCivico() {
    const field = utils.getFirstFieldInGroupsByComponentId(this._groups, 'Conferma civico');
    field.control.actionSets = [];
    return field;
  }

  get nonTroviIlTuoIndirizzoCta() {
    const field =  utils.getFirstFieldInGroupsByComponentId(this._groups, 'manualAddressButton');
    field.control.actionSets = [];
    return field;

  }

  get viewInserimentoManuale() {
    let view = utils.getFirstViewInGroupsByViewId(this._groups, 'InserimentoManualeIndirizzo');
    if (!view) {
      view = utils.getFirstViewInGroupsByViewId(this._groups, 'InserimentoManualeIndirizzoDomicilio');
    }

    const fields = utils.getAllFieldInGroupsNotCloned(view.groups);

    for(const field of fields){
      if(field.visible === true && field.control.type === 'pxButton'){
        if(field.customAttributes.componentID === 'cancelAddressButton'){
          field.control.actionSets = [];
          this.buttonAnnullaInserimentoManuale = JSON.parse(JSON.stringify(field));
          field.visible = false;
        }else if(field.customAttributes.componentID === 'confirmAddressButton'){
          field.control.actionSets = [];
          this.buttonConfermaIndirizzoManuale = JSON.parse(JSON.stringify(field));
          field.visible = false;
        }
      }else{
        if(field?.reference?.toLowerCase().includes('nomestrada')) {
          this._referenceBoxManuale["nomeStrada"] = field.reference;
          // if(this.required)
          //   field.required = true;
        }if(field?.reference?.toLowerCase().includes('numerocivico'))
          this._referenceBoxManuale['civico'] = field.reference;
        if(field?.reference?.toLowerCase().includes('provincia')) {
          this._referenceBoxManuale["provincia"] = field.reference;
          field.disabled = false;
        }
        if(field?.reference?.toLowerCase().includes('comune')) {
          this._referenceBoxManuale["comune"] = field.reference;
        }
        if(field?.reference?.toLowerCase().includes('cap'))
          this._referenceBoxManuale['cap'] = field.reference;
        if(field?.reference?.toLowerCase().includes('stato'))
          this._referenceBoxManuale['stato'] = field.reference;
      }
    }

    return view;
  }

  handleInputPrincipale = (event) => {
    this.inputPrincipaleUtente = event.target.value;

    if (this.inputPrincipaleUtente.length === 0) {
      this.showDropdown = false;
      this.predictions = [];
      this.errorStatus = false;
      this.errorMessage = '';
      return;
    }
    this._searchPredictions();
  }

  handleInputSecondario = (event) => {
    this.inputSecondarioUtente = event.target.value;
  }

  handleInputFocus = () => {
    if (this.predictions.length > 0 && this.inputPrincipaleUtente.length >= 3) {
      this.showDropdown = true;
    }
  }

  handleInputBlur = () => {
    setTimeout(() => {
      this.showDropdown = false;
    }, 200);
  }

  handlePredictionSelect(event) {
    const placeId = event.currentTarget.dataset.placeId;
    if (!placeId) return;

    const selectedPrediction = this.predictions.find(p => p.placeId === placeId);
    if (!selectedPrediction) return;

    this.inputPrincipaleUtente = selectedPrediction.description;
    this.showDropdown = false;
    this._getPlaceDetails(placeId);
  }


  handlePassaggioAutocompletamento = () => {
    this.setCurrentState(AddresModeType.AUTOCOMPLETAMENTO);
    this.errorStatus = false;
    this.errorMessage = '';
  }

  handlePassaggioIndirizzoManuale = (evt) => {
    this.setCurrentState(AddresModeType.MANUALE);
    this.errorStatus = false;
    this.errorMessage = '';
  }

  handleConfermaManuale = (evt) => {
    this.setCurrentState(AddresModeType.AUTOCOMPLETAMENTO);
    this.errorStatus = false;
    this.errorMessage = '';

    const state = getFormState();
    let newAddress = {};

    for(const key in this._referenceBoxManuale){
      const control = state.values[this._referenceBoxManuale[key]];
      newAddress[key] = control.value;
    }
    this._currentAddressInfo = newAddress;

    this._normalizzaIndirizzo();
  }

  _init() {
    if (!this._field || !this._groups) return;

    const isEffectivelyVisible = this._field && this._field.visible !== false;
    const isPegaRequired = this._field && (this._field.required === true || this._field.customAttributes?.required === 'true');
    const effectiveIsRequired = isEffectivelyVisible && isPegaRequired;
  
    if (effectiveIsRequired) {
      setFieldValue(
        this._field.reference,
        this._getFieldValue(),
        'address',
        effectiveIsRequired
      );
    }
     const isEffectivelyVisible = this._field && this._field.visible !== false;
  const isPegaRequired = this._field && (this._field.required === true || this._field.customAttributes?.required === 'true');
  const effectiveIsRequired = isEffectivelyVisible && isPegaRequired;

  if (effectiveIsRequired) {
    setFieldValue(
      this._field.reference,
      this._getFieldValue(),
      'address',
      effectiveIsRequired
    );
  }

  const savedStato = localStorage.getItem('currentStateAddressAutocomplete')
  
    const savedStato = localStorage.getItem('currentStateAddressAutocomplete')

    const savedStato = localStorage.getItem('currentStateAddressAutocomplete');
    if (savedStato && Object.values(AddresModeType).includes(savedStato)) {
          this.currentState = savedStato || AddresModeType.AUTOCOMPLETAMENTO;
    }

    if (this._field?.customAttributes?.['GestioneProcesso.MostraBoxDomicilio']) {
      this.tipoAutocomplete = 'domicilio';
    }
    if (this._field?.customAttributes?.['GestioneProcesso.MostraBoxResidenza']) {
      this.tipoAutocomplete = 'residenza';
    }

    this._setupManualViewButtons();
  }

  _setupManualViewButtons() {
    if (!this.viewInserimentoManuale) return;

    const fields = utils.getAllFieldInGroupsNotCloned(this.viewInserimentoManuale.groups);

    for (const field of fields) {
      if (field.visible === true && field.control && field.control.type === 'pxButton') {
        if (field.customAttributes && field.customAttributes.componentID === 'manualAddressButton') {
          field.control.actionSets = [];
        }
      }
    }
  }

  async _searchPredictions() {
    this.isSearchingPredictions = true;
    this.showDropdown = true;

    try {
      const resultString = await getPlacesAutocomplete({
        input: this.inputPrincipaleUtente
      });
      const result = JSON.parse(resultString);

      if (result.error) {
        this.errorStatus = true;
        this.errorMessage = 'Errore nella ricerca dell\'indirizzo: ' + result.error;
        this.predictions = [];
      } else if (result.predictions && Array.isArray(result.predictions)) {
        this.predictions = result.predictions.map(pred => ({
          placeId: pred.place_id,
          description: pred.description,
          mainText: pred.structured_formatting?.main_text || pred.description,
          secondaryText: pred.structured_formatting?.secondary_text || ''
        }));
        this.showDropdown = this.predictions.length > 0;
      } else {
        this.predictions = [];
        this.showDropdown = false;
      }
    } catch (error) {
      console.error('Error in _searchPredictions:', error);
      this.errorStatus = true;
      this.errorMessage = 'Errore nella ricerca dell\'indirizzo: ' + (error.message || error);
      this.predictions = [];
      this.showDropdown = false;
    } finally {
      this.isSearchingPredictions = false;
    }
  }

  async _normalizzaIndirizzo() {

    if (!this._currentAddressInfo || !this._currentAddressInfo.nomeStrada || !this._currentAddressInfo.comune) {

      this.errorStatus = true;
      this.errorMessage = 'Errore: dati indirizzo incompleti per la normalizzazione.';
      this.processoDiInvioDati = false;
      this.blockSubmit = false;
      return;
    }

    const indirizzoCompleto = `${this._currentAddressInfo.nomeStrada || ''}, ${this._currentAddressInfo.civico || ''}`;

    const richiestaNormalizzaPayload = {
      indirizzo: indirizzoCompleto,
      nomeComune: this._currentAddressInfo.comune || '',
      cap: this._currentAddressInfo.cap || '',
      codiceProvincia: this._currentAddressInfo.provincia || ''
    };

    this.processoDiInvioDati = true;
    this.blockSubmit = true;

    const bff = new BffInterprete({ mockUrls: false }); // mockUrls: false per chiamare l'endpoint reale
    const normalizationUrl = bff.getURLS().getNormalizzationUrl();
    const headers = bff.getCustomHeaders();

    try {
      const fetchResponse = await fetch(normalizationUrl, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(richiestaNormalizzaPayload)
      });

      if (!fetchResponse.ok) {
        const errorText = await fetchResponse.text();
        this.errorStatus = true;
        this.errorMessage = `Errore servizio normalizzazione: ${fetchResponse.status}. Dettaglio: ${errorText || fetchResponse.statusText}`;
        return;
      }

      const response = await fetchResponse.json();

      if (response.esito && response.esito.stato === 'OK') {
        const codiceBelfiore = response.localita?.codiceBelfiore || '';
        this._currentAddressInfo.codiceCatastaleComune = codiceBelfiore;

        const referencesToUpdate = {};
        let basePathPega = '';
        if (this._field?.reference) {
          const refParts = this._field.reference.split('.');
          if (refParts.length > 0) {
            refParts.pop();
            basePathPega = refParts.join('.');
          }
        }

        if (!basePathPega) {
          basePathPega = this._field?.customAttributes?.referencePath ||
            (this.tipoAutocomplete === 'residenza' ? 'Contraente.Residenza' : 'Contraente.Domicilio');
          console.warn(`[addressAutocomplete] basePathPega derivato dal fallback: ${basePathPega}. Reference del campo: ${this._field?.reference}`);
        }

        referencesToUpdate[`${basePathPega}.NomeStrada`] = this._currentAddressInfo.nomeStrada || '';
        referencesToUpdate[`${basePathPega}.NumeroCivico`] = this._currentAddressInfo.civico || '';
        referencesToUpdate[`${basePathPega}.Comune`] = this._currentAddressInfo.comune || '';
        referencesToUpdate[`${basePathPega}.Provincia`] = this._currentAddressInfo.provincia || '';
        referencesToUpdate[`${basePathPega}.Cap`] = this._currentAddressInfo.cap || '';
        referencesToUpdate[`${basePathPega}.Stato`] = this._currentAddressInfo.stato || 'Italia';
        referencesToUpdate[`${basePathPega}.CodiceCatastaleComune`] = this._currentAddressInfo.codiceCatastaleComune || '';

        const actionSetForFieldChange = this._field?.control?.actionSets?.find(as =>
          as.events && as.events.some(ev => ev.event === 'change' || ev.event === 'customAddressConfirmAction')
        );

        if (actionSetForFieldChange?.actions?.some(a => a.action === 'finishAssignment')) {
          const stepSuccessivoKey = 'GestioneProcesso.StepSuccessivo';
          const stepSuccessivoChangeVal = this._field?.customAttributes?.['GestioneProcesso.StepSuccessivo_change'];
          if (stepSuccessivoChangeVal) {
            referencesToUpdate[stepSuccessivoKey] = stepSuccessivoChangeVal; // Es. "Conferma"
          }
        }

        if (Object.keys(referencesToUpdate).length > 0 && actionSetForFieldChange) {

          const evtPayload = {
            target: {
              value: this._getFieldValue(),
              reference: this._field.reference
            },
            preventDefault: () => { }
          };
          const fieldPayload = {
            ...this._field,
            referencesToUpdate: referencesToUpdate,
            control: {
              ...this._field.control,
              actionSets: [actionSetForFieldChange]
            }
          };

          fireEvent('handleFieldChanged', {
            evt: evtPayload,
            field: fieldPayload,
            parentLayout: this.parentLayout || null,
          });
        } else {
          if (Object.keys(referencesToUpdate).length === 0) console.warn('Nessun referenceToUpdate da inviare a Pega.');
          if (!actionSetForFieldChange) console.warn('ActionSet di change/conferma non trovato su this._field per inviare i dati normalizzati a Pega.');
        }
      } else {
        console.error('Errore logico nella normalizzazione (FETCH):', response.esito);
        this.errorStatus = true;
        this.errorMessage = response.esito?.messaggi?.[0]?.testo || response.esito?.messaggio || 'Errore nella normalizzazione dell\'indirizzo (FETCH)';
      }
    } catch (error) {
      console.error('Errore durante la chiamata di normalizzazione (FETCH):', error);
      this.errorStatus = true;
      this.errorMessage = 'Errore di rete o imprevisto nella normalizzazione (FETCH)';
    } finally {
      this.processoDiInvioDati = false;
      this.blockSubmit = false;
    }
  }


  handlePredictionSelect(event) {
    const placeId = event.currentTarget.dataset.placeId;
    const selectedPrediction = this.predictions.find(p => p.placeId === placeId);
    if (!selectedPrediction) return;
    this.inputPrincipaleUtente = selectedPrediction.description;
    this.showDropdown = false;
    this._getPlaceDetails(placeId);
  }

  async _getPlaceDetails(placeId) {
    if (!placeId) {
      this.errorStatus = true;
      this.errorMessage = 'Errore: identificativo del luogo mancante';
      return;
    }

    this.processoDiInvioDati = true;

    try {
      const resultString = await getPlacesDetails({ placeId });

      try {
        const result = JSON.parse(resultString);

        if (result.error) {
          this.errorStatus = true;
          this.errorMessage = 'Errore nel recupero dei dettagli dell\'indirizzo: ' + result.error;
          return;
        }

        if (!result.result) {
          this.errorStatus = true;
          this.errorMessage = 'Errore nel recupero dei dettagli dell\'indirizzo';
          return;
        }
        const addressInfo = addressAutocompleteService.recuperaInformazioniDalPlace(result.result);

        this._currentAddressInfo = addressInfo;

        if (!addressInfo.civico) {
          this.setCurrentState(AddresModeType.INDIRIZZO);
          this.errorStatus = true;
          this.errorMessage = 'Inserisci il numero civico';
          this.inputPrincipaleUtente = addressInfo.nomeStrada || '';
        } else {
          this.setCurrentState(AddresModeType.AUTOCOMPLETAMENTO);
          this.errorStatus = false;
          this.errorMessage = '';

          await this._normalizzaIndirizzo();
        }
      } catch (parseError) {
        this.errorStatus = true;
        this.errorMessage = 'Errore nell\'elaborazione della risposta';
      }
    } catch (error) {
      this.errorStatus = true;
      this.errorMessage = 'Errore nel recupero dei dettagli dell\'indirizzo: ' + (error.message || error);
    } finally {
      this.processoDiInvioDati = false;
    }
  }

  handleConfermaCivico = () => {
    if (!this.inputSecondarioUtente || this.inputSecondarioUtente.trim().length === 0) {
      this.errorStatus = true;
      this.errorMessage = 'Il numero civico è obbligatorio.';
      console.warn('[addressAutocomplete] handleConfermaCivico - Civico mancante.');
      return;
    }

    this._currentAddressInfo = {
      ...this._currentAddressInfo,
      civico: this.inputSecondarioUtente.trim()
    };

    this.errorStatus = false;
    this.errorMessage = '';
    this.setCurrentState(AddresModeType.AUTOCOMPLETAMENTO);

    this._normalizzaIndirizzo();
  }

  _handleAddressSelected(addressInfo) {
    const isPiazza = addressInfo.nomeStrada.toLowerCase().includes('piazza');
    const isLargo = addressInfo.nomeStrada.toLowerCase().includes('largo');
    const isCorso = addressInfo.nomeStrada.toLowerCase().includes('corso');

    const civicoRequired = !isPiazza && !isLargo && !isCorso;

    if (!addressInfo.civico && civicoRequired) {
      this.setCurrentState(AddresModeType.INDIRIZZO);
      this.errorStatus = true;
      this.errorMessage = 'Inserisci il numero civico';
      this.inputPrincipaleUtente = addressInfo.nomeStrada || '';
      if (this.required) this.blockSubmit = true;

      this._currentAddressInfo = addressInfo;
    } else {
      this.setCurrentState(AddresModeType.AUTOCOMPLETAMENTO);
      this.errorStatus = false;
      this.errorMessage = '';
      this._currentAddressInfo = addressInfo;
      this._normalizzaIndirizzo();
    }
  }

  _getFieldValue() {
    if (!this._currentAddressInfo) return '';

    const parts = [];
    if (this._currentAddressInfo.nomeStrada) parts.push(this._currentAddressInfo.nomeStrada);
    if (this._currentAddressInfo.civico) parts.push(this._currentAddressInfo.civico);
    if (this._currentAddressInfo.comune) parts.push(this._currentAddressInfo.comune);
    if (this._currentAddressInfo.nomeProvincia) parts.push(this._currentAddressInfo.nomeProvincia);
    if (this._currentAddressInfo.cap) parts.push(this._currentAddressInfo.cap);

    return parts.join(', ');
  }
}
