.layout {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.w-100 {
  width: 100%;
}

.debug {
  border: 1px solid #cc4fdc;
  padding: 10px;
}

.temporaryLabel {
  color: #cc4fdc;
  font-style: italic;
}

.center-layout {
  display: flex;
  flex-direction: column;
  justify-content: center !important;
  align-items: center !important;
}

.mimic-a-sentence {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: baseline;
  gap: 0.2em;

}

.mimic-a-sentence {
  width: fit-content !important;
}

.mimic-a-sentence .caption {
  background-color: #cc4fdc !important;
}

.mimic-a-sentence-center-web {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: baseline;
  gap: 0.2em;
  justify-content: center;
}

.accordion-header {
  height: 500px;
  width: 450px;
  border: solid 1px #333333;
  background-color: white;
  padding: 15px 20px;
  border-radius: 12px;
}

.Accordion-8 {
  max-width: 1062px;
  width: 100%;
  margin: 0 auto;
  background-color: #e2f0f9;
  border: solid 1px #8ab5d1;
  border-radius: 24px;
  padding: 20px;
}

.accordion-content {
  padding: 15px 20px;
  background-color: white;
  border: solid 1px var(--dark-light_blue);
  border-top: none;
}


.mimic-a-sentence-center {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: baseline;
  gap: 0.2em;
  justify-content: center;
}

.default {
  display: flex;
  flex-direction: row;
  justify-content: center;
}

.default .sub-layout {
  width: 100%;
  padding: 0;
}

.box-privacy {
  display: flex;
  flex-direction: column;
  width: 100%;
  border: 2px solid #f1f1f1;
  gap: 16px;
  padding: 16px;
  margin: 0 auto;
}

.box-privacy .sub-layout {
  display: flex;
  gap: 16px;
  flex-direction: row;
}

/*
 * Layout utilizzato per colorare lo sfondo
 */
.default-bg-grey {
  background-color: var(--border-card-disabled);
  flex-grow: 1;
  min-height: 100%;
  height: 100%;
}

.default-bg-grey.rounded {
  border-top-right-radius: 24px;
  border-top-left-radius: 24px;
}

@media (max-width: 767px) {
  .default-bg-grey.rounded {
    padding: 16px 16px 24px 16px;
    /* Assuming $mobilePadding is 16px 16px 24px 16px */
  }
}

@media (min-width: 768px) and (max-width: 1024px) {
  .default-bg-grey.rounded {
    padding: 24px 0;
  }
}



/**
 * Card con sfondo bianco e bordi superiori arrotondati
 */

.card {
  background-color: white;
  padding: 18px 18px;
  border-radius: 24px;
  border: 1px solid #c2bdbd;
}

.card::before {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: 22px;
  padding-top: 5px;
  border-radius: 12px;
  border: solid 2px #e4eff5;
}

.card::after {
  top: 50%;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: #000;
  transform: translateY(-50%);
}

.card-bg-white {
  flex-grow: 1;
  min-height: 100%;
  height: 100%;

  background-color: white;
  border-top-left-radius: 24px;
  border-top-right-radius: 24px;
}

.card-form {
  display: flex;
  flex-direction: row;
  justify-content: center;
  background-color: white;
  width: 100%;
}

.card-form>.sub-layout {
  border: solid 2px #f0f0f0;
  max-width: 100%;
  width: 100%;
  padding: 32px 60px;
}

/* Media queries per responsive design */
@media screen and (max-width: 480px) {
  .card-form>.sub-layout {
    width: 100%;
    padding: 24px 24px 28px;
  }

  .card-form .input-dati-utente {
    width: 100% !important;
  }
}

@media screen and (min-width: 481px) and (max-width: 768px) {
  .card-form>.sub-layout {
    max-width: 720px;
    /* Equivalente a $width_container_tablet */
    width: 100%;
    padding: 32px 60px;
  }
}

@media screen and (min-width: 769px) {
  .card-form>.sub-layout {
    max-width: 1140px;
    /* Equivalente a $width_container_desktop */
    width: 100%;
    padding: 32px 60px;
  }
}

/* Stili aggiuntivi per elementi interni */
.card-form .accordion {
  width: 100% !important;
}

@media screen and (max-width: 480px) {

  .card-form .layout-input,
  .card-form .layout-input-date {
    width: 100% !important;
  }
}

.card-form .box-indirizzo-width {
  width: 100%;
  min-width: 0;
}

.card-form .radio-size {
  max-width: none;
  width: 45%;
}

.card-form .radio-horizontal {
  width: 100%;
}

.card-form .responsive3col .pdn-label-input,
.card-form .responsive4col .pdn-label-input {
  padding: 0 !important;
}

.card-form .responsive3col .select,
.card-form .responsive4col .select {
  margin-bottom: 0 !important;
}

.card-form .responsive3col input,
.card-form .responsive4col input {
  margin: 0 !important;
}

.box-web-border-gray1 {
  border: 1px solid #d8c8c8;
  border-radius: 8px;
  padding: 16px;
  margin: 16px;
}

.row-layout {
  display: flex;
  flex-direction: row;
}

@media screen and (max-width: 480px) {
  .row-layout {
    align-items: var(--align-items-m);
  }
}

@media screen and (max-width: 768px) {
  .row-layout {
    align-items: var(--align-items-t);
  }
}

@media screen and (min-width: 768px) {
  .row-layout {
    align-items: var(--align-items-d);
  }
}

.row-layout,
.col-layout {
  --gap-mobile: 0;
  --gap-tablet: 0;
  --gap-desktop: 0;
  --justify-content-m: "center";
  --align-items-m: "center";
  --justify-content-t: "center";
  --align-items-t: "center";
  --justify-content-d: "center";
  --align-items-d: "center";
  --align-content-d: "initial";
  --align-content-t: "initial";
  --align-content-m: "initial";
  --grid-template-columns-d: "none";
  --grid-template-columns-t: "none";
  --grid-template-columns-m: "none";
}

@media screen and (max-width: 480px) {

  .row-layout,
  .col-layout {
    gap: var(--gap-mobile);
    justify-content: var(--justify-content-m);
    align-content: var(--align-content-m);
    grid-template-columns: var(--grid-template-columns-m);
    align-items: var(--align-items-m);
  }
}

@media screen and (max-width: 768px) {

  .row-layout,
  .col-layout {
    gap: var(--gap-tablet);
    justify-content: var(--justify-content-t);
    align-content: var(--align-content-t);
    grid-template-columns: var(--grid-template-columns-t);
    align-items: var(--align-items-t);
  }
}

@media screen and (min-width: 768px) {

  .row-layout,
  .col-layout {
    gap: var(--gap-desktop);
    justify-content: var(--justify-content-d);
    align-content: var(--align-content-d);
    grid-template-columns: var(--grid-template-columns-d);
    align-items: var(--align-items-d);
  }
}

.tooltip-card {
  display: block;
  height: auto;
}

.price-container {
  display: grid;
  align-items: center;
  justify-content: space-between;
  gap: -1rem;
  padding: 1rem;
  border: 1px solid #ddd;
  border-radius: 2px;
  background-color: #ffffff;
  position: relative;
  font-size: 20px;
  margin: 16px;
}

.price-container::after {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  left: 50%;
  width: 1px;
  background-color: #ccc;
  transform: translateX(-50%);
}

.responsive-2col {
  display: flex;
  flex-direction: row;
  justify-content: space-evenly;
}

.responsive-2colPU {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.responsive-2col8 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  column-gap: 24px;
  row-gap: 28px;
}

.responsive-2col1fr2fr {
  display: grid;
  grid-template-columns: "a b" auto / 1fr 2fr;
  align-items: flex-end;
}

.responsive-3col {
  display: flex;
  flex-direction: column;
  row-gap: 20px;
  align-items: baseline;
}

@media (min-width: 768px) {
  .responsive-3col {
    display: grid;
    grid-template-columns: 1fr 1fr;
    column-gap: 24px;
    row-gap: 28px;
    align-items: start;
  }
}

@media (min-width: 1025px) {
  .responsive-3col {
    grid-template-columns: 1fr 1fr 1fr;
  }
}



.responsiveAcolDrowTrowMcol {
  display: flex;
  flex-direction: row;
  align-items: center;
}

@media (max-width: 768px) {
  .responsiveAcolDrowTrowMcol {
    flex-direction: column;
    align-items: stretch;
  }
}

@media (min-width: 769px) {
  .responsiveAcolDrowTrowMcol {
    flex-direction: column;
    ;
  }
}


/*.inline-middle-layout {
  display: flex;
  flex-direction: row;
  justify-content: center;
  width: 100%;
}*/

.rounded-card {
  background-color: #ffffff;
  color: var(--main_color);
  font-family: var(----font-family-bold);
  border: 1px inset var(--main_color);
  border-radius: 12px;
}

.inlineTPD-middle {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 10px;
}

.carrello-PU-Header {
  background-color: #3d486c;
}

.unicoProtezione-Ribbon {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 4px;
}

.flat-card {
  --cardBorder: var(--border-card-disabled, #ccc);
  /* Usa un valore di fallback */
  --cardBackground: var(--white, #fff);
  --textColor: var(--blue-primary, #000);

  border: solid 1px var(--cardBorder);
  background-color: var(--cardBackground);
  color: var(--textColor);

  min-height: auto !important;
  height: auto !important;
}

.flat-card label {
  --markerBorderColor: var(--blue-primary, #0000ff);
  --markerBackgroundColor: var(--white, #fff);
  --markerBorderSize: 2px;

  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  gap: 8px;
  cursor: pointer;

  min-height: auto !important;
  height: auto !important;
  padding: 16px;
}

input[type="radio"]:checked~.FlatCard {
  --cardBorder: var(--blue-primary, #0000ff);
  --cardBackground: var(--blue-primary, #0000ff);
  --textColor: var(--white, #fff);
}

input[type="radio"]:checked~.FlatCard label {
  --markerBorderColor: var(--color-green, #00ff00);
  --markerBackgroundColor: var(--color-green, #00ff00);
  --markerBorderSize: 0;
}

.AssurancePackageRibbon {
  max-width: 600px;
  width: 100%;
  box-sizing: border-box;
}

.col-layout {
  display: flex;
  flex-direction: column;
  align-items: stretch !important;
  width: 100%;
}

.row-layout {
  display: flex;
  flex-direction: row !important;
}

.Carousel {
  display: flex;
  justify-content: center;
}

.inline-gridXY {
  display: grid;
  gap: 2%;
  grid-template-columns: 70% 28%;
  align-items: start;
}

.inline-gridXY .layout-input {
  width: 100% !important;
}

.inline-gridXY .icon-Info2 {
  text-align: end;
  margin-right: 25px;
}

.inline-gridXY .tooltip-container {
  width: initial;
}

.CardWithBg {
  margin-top: 20px;
  margin-bottom: 20px;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 10px 3%;
  background-color: var(--secondary-lightest);
  width: 100%;
}

.CardWithBg .mimicASentence {
  align-items: center;
  gap: 1.5em;
}

.tooltip-card-container {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  max-width: 1062px;
  width: 100%;
  margin: 0 auto;
  gap: 8px;
  padding: 12px;
  background-color: #EEF6FC;
}

.tooltip-card-container-icon {
  content: "";
  width: 20px;
  height: 20px;
  margin-top: 3px;
  flex-shrink: 0;
  display: block;
  background-size: cover;
}

.tooltip-card-container .min-w {
  min-width: unset;
}