<template>
  <template if:true={isVisible}>
    <div class={componentClass}>
      <p if:true={debug} class="temporaryLabel">LAYOUT: LayoutFormat {layout.layoutFormat}</p>
      <p if:true={debug} class="temporaryLabel">groupFormat: {layout.groupFormat}</p>

      <template if:true={hasDynamicLayoutRows}>
        <span if:true={debug}>hasDynamicLayoutRows</span>
        <div style={computedStyles} class={groupFormat}>
          <template for:each={layout.rows} for:index="indexRow" for:item="row">
            <template for:each={row.groups} for:index="index" for:item="group">
              <c-groups key={key} groups={group} debug={debug} group-format={layoutGroupFormat}></c-groups>
            </template>
          </template>
        </div>
      </template>

      <!-- GROUPS WITH GROUP CLASS -->
      <template if:true={hasGroupClass}>
        <span if:true={debug}>hasGroupClass</span>
        <div class={groupFormat} style={computedStyles}>
          <template for:each={visibleGroups} for:index="index" for:item="group">
            <c-groups key={key} groups={group} debug={debug} group-format={layoutGroupFormat}></c-groups>
          </template>
        </div>
      </template>

      <!-- GROUPS WITH CUSTOM COMPONENT CLASS -->
      <template if:true={hasCustomComponentClass}>
        <span if:true={debug}>hasCustomComponentClass</span>
        <div class={groupFormat}>
          <template for:each={visibleGroups} for:index="index" for:item="group">
            <c-groups key={key} groups={group} computed-styles={computedStyles} custom-component={visibleGroups}
              debug={debug}></c-groups>
          </template>
        </div>
      </template>

      <!-- ACCORDION GROUPS -->
      <template if:true={isAccordionOpenedOrClosed}>
        <span if:true={debug}>isAccordionOpenedOrClosed</span>
        <div class="inline-middle-layout w-100">
          <template for:each={layout.groups} for:index="index" for:item="group">
            <c-groups key={key} groups={group} debug={debug} computed-styles={computedStyles}
              group-format={layoutGroupFormat}></c-groups>
          </template>
        </div>
      </template>

      <!-- ACCORDION N -->
      <template if:true={isAccordionN}>
        <span if:true={debug}>isAccordionN</span>
        <c-dx-accordion groups={layout.groups} debug={debug} title-id={layout.title}
          accordion-type={accordionType}></c-dx-accordion>
      </template>

      <template if:true={isCardForm}>
        <div class="card-form">
          <div class="sub-layout">
            <template for:each={layout.groups} for:item="group" for:index="index">
              <c-groups key={key} custom-attributes={customAttributes} groups={group} rows={layout.rows}></c-groups>
            </template>
          </div>
        </div>
      </template>

      <!-- FlatCard -->
      <template if:true={isFlatCardLayout}>
        <span if:true={debug}>isFlatCard</span>
        <template for:each={layout.groups} for:index="index" for:item="group">
          <c-dx-flat-card-layout key={key} groups={group}></c-dx-flat-card-layout>
        </template>
      </template>

      <!-- Responsive AcolDrowTrowMcol -->
      <template if:true={isAColDRowTRowMCol}>
        <div class="responsiveAcolDrowTrowMcol">
          <template for:each={layout.groups} for:index="index" for:item="group">
            <c-groups key={key} groups={group} rows={layout.rows}></c-groups>
          </template>
        </div>
      </template>

      <!-- RESPONSIVE PADDING CONTAINER -->
      <template if:true={isPuPaddingLayout}>
        <span if:true={debug}>isPuPaddingLayout</span>
        <c-pu-padding-layout group-format={layout.groupFormat}>
          <template for:each={visibleGroups} for:index="index" for:item="group">
            <c-groups key={key} groups={group} debug={debug} group-format={layoutGroupFormat}></c-groups>
          </template>
        </c-pu-padding-layout>
      </template>

      <!-- TooltipCard -->
      <template if:true={isTooltipCard}>
        <span if:true={debug}>isTooltipCard</span>
        <div class="tooltip-card-container">
          <div class="tooltip-card-container-icon" style={iconInfoPu}></div>
          <div class={groupFormat} style={computedStyles}>
            <template for:each={visibleGroups} for:index="index" for:item="group">
              <c-groups key={key} groups={group} debug={debug} group-format={layoutGroupFormat}></c-groups>
            </template>
          </div>
        </div>
      </template>

      <!-- DEFAULT GROUPS -->
      <template if:true={isDefault}>
        <span if:true={debug}>isDefault</span>
        <div class={groupFormat}>
          <div class="sub-layout">
            <template for:each={layout.groups} for:index="index" for:item="group">
              <c-groups key={key} groups={group} debug={debug} computed-styles={computedStyles}
                group-format={layoutGroupFormat}></c-groups>
            </template>
          </div>
        </div>
      </template>

      <!-- VIEW -->
      <template if:true={layout.view}>
        <span>layout.view</span>
        <c-view view={layout.view} debug={debug}></c-view>
      </template>
    </div>
  </template>
</template>