import { utils } from 'c/utils';
import { LightningElement, api } from 'lwc';

const REGEX_STYLE_PARAGRAPH = /data-pega-style="([^"]*)"/;

export default class FieldParagraph extends LightningElement {
  _field;

  decodedValue;
  dataPegaStyle;
  renderPlainText;
  @api publicPegaStyle;

  @api debug;

  @api
  get field() {
    return this._field;
  }

  set field(input) {
    this._field = input;
    const elemAttr = input?.value ? input.value.match(REGEX_STYLE_PARAGRAPH) : undefined;
    
    if (elemAttr) {
      if (input?.value) {        
        const cleanedTextValue = elemAttr[0]
        ? input?.value.replace(elemAttr[0], '')
        : input?.value;
        
        this.decodedValue = utils.decodeHTML(cleanedTextValue);
        this.dataPegaStyle = elemAttr[1];
        this.renderPlainText = true;
      }
    } else {
      this.decodedValue = input.value
      this.dataPegaStyle = null;
      this.renderPlainText = false;
    }
  }
  
  get plainText() {
    return this.decodedValue.replace(/<[^>]*>/g, '');
  }

  get format() {
    return this.dataPegaStyle ? `html-content ${utils.getClassFromFormat(this.dataPegaStyle)}` : `html-content`
  }

  get componentClass() {
    return `paragraph ${this.debug ? 'debug' : ''}`.trim();
  }

  renderedCallback() {
    const htmlContent = this.template.querySelector('.html-content');
    if (htmlContent) {
      htmlContent.innerHTML = this.decodedValue;
    }
  }

}
