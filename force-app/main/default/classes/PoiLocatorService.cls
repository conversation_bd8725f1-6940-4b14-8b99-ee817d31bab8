public with sharing class PoiLocatorService {

    private static final String CLIENT_ID = 'c13117fd-af7e-45b6-8418-ff7b32a7422f'; // Metti qui il client id reale o usa Named Credential
    private static final String CLIENT_SECRET = 'bS5qE8iM2yW8eL0qM7fU7pI2pN2dK3yP0cM7oM3gI1xW0mG6bS'; // Metti qui il client secret reale o usa Named Credential
    private static final String BASE_URL = 'https://evo-dev.unipolsai.it/api/pub/poiLocator/v1/bounds/poi';

    @AuraEnabled
    public static String getAgenzie(Double lat, Double lng, Integer maxItem) {
        try {
            HttpRequest req = new HttpRequest();
            req.setEndpoint(BASE_URL + '?category=AGENZIA&lat=' + lat + '&lng=' + lng + '&maxItem=' + maxItem);
            req.setMethod('GET');
            req.setHeader('Content-Type', 'application/json');
            req.setHeader('x-ibm-client-id', CLIENT_ID);
            req.setHeader('x-ibm-client-secret', CLIENT_SECRET);

            Http http = new Http();
            HttpResponse res = http.send(req);

            if (res.getStatusCode() == 200) {
                return res.getBody();
            } else {
                System.debug('Errore chiamata POI Locator: ' + res.getStatusCode() + ' ' + res.getStatus());
                System.debug('Risposta: ' + res.getBody());
                return JSON.serialize(new Map<String, Object>{
                    'error' => 'Errore nella chiamata API: ' + res.getStatusCode() + ' ' + res.getStatus()
                });
            }
        } catch (Exception e) {
            System.debug('Eccezione in getAgenzie: ' + e.getMessage());
            return JSON.serialize(new Map<String, Object>{
                'error' => 'Errore: ' + e.getMessage()
            });
        }
    }

    // Se vuoi deserializzare in oggetti Apex, metti queste classi fuori da questa classe o in un file a parte

    public class PoiResponse {
        public String httpCode;
        public String httpMessage;
        public String moreInformation;
        public List<Poi> pois;
    }

    public class Poi {
        public String id;
        public String name;
        public Double lat;
        public Double lng;
    }
}
