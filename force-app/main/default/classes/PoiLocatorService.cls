public with sharing class PoiLocatorService {
    
    // Endpoint del Named Credential che punta al middleware
    private static final String ENDPOINT_PREFIX = 'callout:POI_BFF';

    @AuraEnabled(cacheable=true)
    public static String getAllAgenzie(Double lat, Double lng, Integer maxItem) {
        try {
            // Costruzione dell’endpoint dinamico
            String fullEndpoint = ENDPOINT_PREFIX + '/api/poi/agenzie'
                + '?lat=' + lat
                + '&lng=' + lng
                + '&maxItem=' + maxItem;

            HttpRequest req = new HttpRequest();
            req.setEndpoint(fullEndpoint);
            req.setMethod('GET');
            req.setHeader('Content-Type', 'application/json');

            Http http = new Http();
            HttpResponse res = http.send(req);

            if (res.getStatusCode() == 200) {
                return res.getBody();
            } else {
                return JSON.serialize(new Map<String, Object>{
                    'error' => 'Errore dal BFF: ' + res.getStatusCode() + ' - ' + res.getStatus()
                });
            }

        } catch (Exception e) {
            return JSON.serialize(new Map<String, Object>{
                'error' => 'Eccezione in getAllAgenzie: ' + e.getMessage()
            });
        }
    }
}
