public with sharing class GoogleMapsProxy {
    private static final String API_KEY = 'AIzaSyCOyfBmSpLkC7mpvBN0gfu20R-pANS2tMU';
    private static final String PLACES_AUTOCOMPLETE_URL = 'https://maps.googleapis.com/maps/api/place/autocomplete/json';
    private static final String PLACES_DETAILS_URL = 'https://maps.googleapis.com/maps/api/place/details/json';
    
    @AuraEnabled
    public static String getPlacesAutocomplete(String input) {
        try {
            // Verifica input
            if (String.isBlank(input)) {
                return JSON.serialize(new Map<String, Object>{
                    'error' => 'Input non valido',
                    'predictions' => new List<Object>()
                });
            }
            
            // Costruisci URL
            String endpoint = PLACES_AUTOCOMPLETE_URL + 
                '?input=' + EncodingUtil.urlEncode(input, 'UTF-8') + 
                '&components=country:it' +
                '&language=it' +
                '&types=address' +
                '&key=' + API_KEY;
            
            // Esegui chiamata
            Http http = new Http();
            HttpRequest request = new HttpRequest();
            request.setEndpoint(endpoint);
            request.setMethod('GET');
            request.setTimeout(20000); // 20 secondi di timeout
            
            HttpResponse response = http.send(request);
            
            // Verifica risposta
            if (response.getStatusCode() == 200) {
                return response.getBody();
            } else {
                System.debug('Errore nella chiamata Places Autocomplete: ' + response.getStatusCode() + ' ' + response.getStatus());
                System.debug('Risposta: ' + response.getBody());
                
                return JSON.serialize(new Map<String, Object>{
                    'error' => 'Errore nella chiamata API: ' + response.getStatusCode() + ' ' + response.getStatus(),
                    'predictions' => new List<Object>()
                });
            }
        } catch (Exception e) {
            System.debug('Eccezione in getPlacesAutocomplete: ' + e.getMessage() + '\n' + e.getStackTraceString());
            
            return JSON.serialize(new Map<String, Object>{
                'error' => 'Errore: ' + e.getMessage(),
                'predictions' => new List<Object>()
            });
        }
    }
    
    @AuraEnabled
    public static String getPlaceDetails(String placeId) {
        try {
            // Verifica placeId
            if (String.isBlank(placeId)) {
                return JSON.serialize(new Map<String, Object>{
                    'error' => 'PlaceId non valido'
                });
            }
            
            // Costruisci URL
            String endpoint = PLACES_DETAILS_URL + 
                '?place_id=' + EncodingUtil.urlEncode(placeId, 'UTF-8') + 
                '&fields=address_components,formatted_address,geometry,name,place_id' +
                '&language=it' +
                '&key=' + API_KEY;
            
            // Esegui chiamata
            Http http = new Http();
            HttpRequest request = new HttpRequest();
            request.setEndpoint(endpoint);
            request.setMethod('GET');
            request.setTimeout(20000); // 20 secondi di timeout
            
            HttpResponse response = http.send(request);
            
            // Verifica risposta
            if (response.getStatusCode() == 200) {
                // Analizza la risposta per verificare che sia valida
                Map<String, Object> responseMap = (Map<String, Object>)JSON.deserializeUntyped(response.getBody());
                String status = (String)responseMap.get('status');
                
                if (status == 'OK') {
                    return response.getBody();
                } else {
                    System.debug('Errore nella risposta Places Details: ' + status);
                    System.debug('Risposta: ' + response.getBody());
                    
                    return JSON.serialize(new Map<String, Object>{
                        'error' => 'Errore nella risposta API: ' + status
                    });
                }
            } else {
                System.debug('Errore nella chiamata Places Details: ' + response.getStatusCode() + ' ' + response.getStatus());
                System.debug('Risposta: ' + response.getBody());
                
                return JSON.serialize(new Map<String, Object>{
                    'error' => 'Errore nella chiamata API: ' + response.getStatusCode() + ' ' + response.getStatus()
                });
            }
        } catch (Exception e) {
            System.debug('Eccezione in getPlaceDetails: ' + e.getMessage() + '\n' + e.getStackTraceString());

            return JSON.serialize(new Map<String, Object>{
                'error' => 'Errore: ' + e.getMessage()
            });
        }
    }

    @AuraEnabled
    public static String getAgencies(String url) {
        try {
            // Verifica URL
            if (String.isBlank(url)) {
                return JSON.serialize(new Map<String, Object>{
                    'error' => 'URL non valido',
                    'agencies' => new List<Object>()
                });
            }

            // Esegui chiamata
            Http http = new Http();
            HttpRequest request = new HttpRequest();
            request.setEndpoint(url);
            request.setMethod('GET');
            request.setHeader('Content-Type', 'application/json');
            request.setHeader('x-ibm-client-id', 'c13117fd-af7e-45b6-8418-ff7b32a7422f');
            request.setHeader('x-ibm-client-secret', 'bS5qE8iM2yW8eL0qM7fU7pI2pN2dK3yP0cM7oM3gI1xW0mG6bS');
            request.setTimeout(30000); // 30 secondi di timeout

            HttpResponse response = http.send(request);

            // Verifica risposta
            if (response.getStatusCode() == 200) {
                return response.getBody();
            } else {
                System.debug('Errore nella chiamata Agencies: ' + response.getStatusCode() + ' ' + response.getStatus());
                System.debug('Risposta: ' + response.getBody());

                return JSON.serialize(new Map<String, Object>{
                    'error' => 'Errore nella chiamata API: ' + response.getStatusCode() + ' ' + response.getStatus(),
                    'agencies' => new List<Object>()
                });
            }
        } catch (Exception e) {
            System.debug('Eccezione in getAgencies: ' + e.getMessage() + '\n' + e.getStackTraceString());

            return JSON.serialize(new Map<String, Object>{
                'error' => 'Errore: ' + e.getMessage(),
                'agencies' => new List<Object>()
            });
        }
    }
}
