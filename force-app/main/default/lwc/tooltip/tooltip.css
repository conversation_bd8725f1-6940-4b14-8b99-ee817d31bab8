.tooltip-groups-container {
    cursor: pointer;
}

.box-tooltip {
    display: flex;
    background: #0f3250;
    color: white;
    padding: 12px;
    z-index: 1000;
    position: absolute;
    font-family: "Unipol";
    min-width: 300px;
    justify-content: space-between;
    width: 300px;
    height: 256px;
}

.box-tooltip::after {
    content: "";
    position: absolute;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: #0f3250 transparent transparent transparent;
}

/* Posizionamento: right-top */
.right-top {
    left: 0;
    bottom: 30px;
}

.right-top::after {
    top: 100%;
    left: 3%;
}

/* Posizionamento: left-top */
.left-top {
    bottom: 30px;
    right: 0;
}

.left-top::after {
    top: 100%;
    right: 3%;
}

/* Posizionamento: right-bottom */
.right-bottom {
    left: 1px;
    top: 25px;
}

.right-bottom::after {
    transform: rotate(180deg);
    bottom: 100%;
    left: 3%;
}

/* Posizionamento: left-bottom */
.left-bottom {
    top: 25px;
    right: 0;
}

.left-bottom::after {
    transform: rotate(180deg);
    bottom: 100%;
    right: 3%;
}

.unipol-popup-background {
    background-color: transparent;
    opacity: 0.9;
    position: fixed;
    display: flex;
    top: 0;
    left: 0;
    margin: auto;
    width: 100%;
    height: 100%;
    z-index: 999;
}

[class^="icon-"] {
  font-family: var(--font-family-icon) !important;
  font-style: normal;
  font-weight: 400;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-Chiudi::before {
  content: "\e933";
}