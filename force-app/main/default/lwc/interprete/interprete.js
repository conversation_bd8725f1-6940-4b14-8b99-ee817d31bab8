/// <reference path="./interprete.jsdoc.js" />

import BffInterprete, { ACTION_LOAD_REQUEST, PEGA_API } from 'c/bffInterprete';
import { utils } from 'c/utils';
import { utilsPega } from 'c/utilsPega';
import { LightningElement, api, track } from 'lwc';

import { registerListener, unregisterListener } from 'c/pubsub';

/** @type {ILoadPageRequest} */
const MOCK_PAYLOAD = {
  action: 'CREATE',
  env: 'UNICO',
  productType: 'PUVEICOLO',
  referencesToUpdate: {
    'Ambito.Bene.Auto.TipoVeicolo': '1',
    'Ambito.Bene.Auto.Targa': 'GG810CV',
    'Ambito.Proprietario.DataDiNascita': '18/06/1956',
    'Ambito.Bene.Auto.RecuperoClasseAltroVeicolo': '',
    'Ambito.Proprietario.TipoPersona': 'PF',
    'Ambito.Proprietario.Residenza.NomeStrada': '<PERSON>',
    'Ambito.Proprietario.Residenza.NumeroCivico': '4',
    'Ambito.Proprietario.Residenza.Comune': 'Mogliano Veneto',
    'Ambito.Proprietario.Residenza.Provincia': 'TV',
    'Ambito.Proprietario.Residenza.Cap': '31021',
    'Ambito.Proprietario.Residenza.Stato': 'Italia',
    'Ambito.Proprietario.Residenza.CodiceCatastaleComune': 'F269',
  },
};

/** @type {ILoadPageRequest} */
const MOCK_PAYLOAD_NEW = {
  action: 'retrieve',
  env: 'UNICO',
  productType: 'PUVEICOLO',
  referencesToUpdate: {
    'Ambito.Bene.Auto.TipoVeicolo': '1',
    'Ambito.Bene.Auto.Targa': 'GG810CV',
    'Ambito.Proprietario.DataDiNascita': '18/06/1956',
    'Ambito.Bene.Auto.RecuperoClasseAltroVeicolo': '',
    'Ambito.Proprietario.TipoPersona': 'PF',
    'Ambito.Proprietario.Residenza.NomeStrada': 'Via Giuseppe Garibaldi',
    'Ambito.Proprietario.Residenza.NumeroCivico': '4',
    'Ambito.Proprietario.Residenza.Comune': 'Mogliano Veneto',
    'Ambito.Proprietario.Residenza.Provincia': 'TV',
    'Ambito.Proprietario.Residenza.Cap': '31021',
    'Ambito.Proprietario.Residenza.Stato': 'Italia',
    'Ambito.Proprietario.Residenza.CodiceCatastaleComune': 'F269',
  },
};

const MOCK_PAYLOAD_UPDATE = {
  assignmentId: 'ASSIGN-WORKLIST UG-INS-PU-WORK-QUOTAZIONE Q-74493!CONFIGURAZIONEOFFERTA',
  actionId: 'PopupVediDati',
  referencesToUpdate: {
    'Contraente.Contatti.Cellulare': '3485269605',
    'Contraente.Contatti.Email': '<EMAIL>',
  },
  productType: 'PUVEICOLO',
};

export default class Interprete extends LightningElement {
  @api request;
  @api debug;

  @track isLoading = false;
  @track reloadVisible = false;

  //TODO: handle configurazione interprete
  config = {
    productType: '',
    env: 'UNICO',
  };

  _bffInterprete = new BffInterprete(this.config);

  view;
  emptyView = { groups: [], visible: true };

  caseData = {};

  @track showFailurePage = false;
  @api errorMessage = '';

  constructor() {
    super();
  }

  handlePageError(error) {
    // Check the error status code if available
    const statusCode = error?.status || error?.body?.status;

    if (statusCode === 404) {
      this.errorMessage = 'La pagina richiesta non è stata trovata';
    } else if (statusCode === 503) {
      this.errorMessage = 'Il servizio è momentaneamente non disponibile';
    } else if (statusCode >= 400 && statusCode < 500) {
      this.errorMessage = 'Si è verificato un errore nella richiesta';
    } else {
      this.errorMessage = 'Servizio momentaneamente non disponibile';
    }
    this.showFailurePage = true;
  }

  /////////////////////////////////////////////////////////////////
  ///////////////////////////// MODAL /////////////////////////////
  /////////////////////////////////////////////////////////////////

  showModal = false;
  modalView;

  closeModal = () => {
    this.modalView = undefined;
    this.showModal = false;
  };

  async runLoadPage() {
    console.log(this.request);
    this.retrieveData(PEGA_API.loadPage, this.request);
  }

  /////////////////////////////////////////////////////////////////
  ///////////////////////// EVENT HANDLERS ////////////////////////
  /////////////////////////////////////////////////////////////////

  /**
   * Generates an event handler for the provided field and parent layout.
   *
   * @param {Object} field - The field object containing control and actionSets.
   * @param {Object} parentLayout - The parent layout object (not used in the current implementation).
   * @returns {EventHandler} - The generated event handler function.
   */
  generateEventHandler(field, parentLayout) {
    const actionsList = this.getActionsList(field, parentLayout);

    const eventHandler = (evt) => {
      actionsList.reduce((promise, actionHandler) => {
        return promise.then(() => actionHandler.handler(evt, actionHandler.data));
      }, Promise.resolve());
    };

    return eventHandler;
  }

  handleFieldClicked(event) {
    const { evt, field, parentLayout } = event;

    console.log(
      '[TEST] handleFieldClicked',
      '\n\nEVT:\n',
      utils.printObject(evt),
      evt,
      '\n\nFIELD:\n',
      utils.printObject(field),
      '\n\nPARENTLAYOUT:\n',
      utils.printObject(parentLayout)
    );

    if (!field) return;
    const eventHandler = this.generateEventHandler(field, parentLayout);

    if (!eventHandler) return;
    eventHandler(evt);
  }

  handlerRetryClicked(request) {
    const { productType } = request;
    this.config.productType = productType;

    this.retrieveData(PEGA_API.loadPage, this._bffInterprete.requestMapper[PEGA_API.loadPage]('reload'));
  }

  handleFieldChanged = (event) => {
    const { evt, field, parentLayout } = event;

    if (!evt) return;

    console.log(
      '[TEST] handleFieldChanged',
      '\n\nEVT:\n',
      utils.printObject(evt),
      '\n\nFIELD:\n',
      utils.printObject(field),
      '\n\nPARENTLAYOUT:\n',
      utils.printObject(parentLayout)
    );

    let value;
    let reference = evt?.target?.dataset?.reference || field.reference;

    if (field.control.type === utilsPega.fields.FIELD_TYPES.pxCheckbox) {
      value = evt?.target?.checked;
    } else if (field.control.type === utilsPega.fields.FIELD_TYPES.pxRadio) {
      const input = evt?.currentTarget?.querySelector?.('input[type="radio"]');
      value = evt?.target?.value || input?.value;
    } else {
      if(field.control.type === utilsPega.fields.FIELD_TYPES.pxDateTime) {
        value = utils.formatDateByField(evt?.target?.value,field);
      } else {  
        value = evt?.target?.value;
      }
      
    }

    // if (this.isDate(field)) {
    //   value = value.replaceAll("-", "");
    // }

    if (!reference) return;
    if (value === this.caseData[reference]) return;

    this.caseData[reference] = String(value);

    const eventHandler = this.generateEventHandler(field, parentLayout);

    if (!eventHandler) return;
    eventHandler(evt, field);
  };

  /////////////////////////////////////////////////////////////////
  //////////////////////// ACTION HANDLERS ////////////////////////
  /////////////////////////////////////////////////////////////////

  handleLocalAction = async (evt, data) => {
    const actionData = data?.actionData;
    const referencesToUpdate = data?.referencesToUpdate;

    console.log(
      '[TEST] handleLocalAction',
      '\n\nEVT:\n',
      utils.printObject(evt),
      evt,
      '\n\nDATA:\n',
      utils.printObject(data)
    );

    this.retrieveData(
      PEGA_API.updatePage,
      this._bffInterprete.requestMapper[PEGA_API.updatePage]({
        actionId: actionData?.localAction,
        referencesToUpdate: referencesToUpdate,
      }),
      actionData?.target
    );
  };

  handlePostValue = async (evt, data) => {
    const actionData = data?.actionData;
    const referencesToUpdate = data?.referencesToUpdate;

    console.log(
      '[TEST] handlePostValue',
      '\n\nEVT:\n',
      utils.printObject(evt),
      evt,
      '\n\nDATA:\n',
      utils.printObject(data)
    );

    this.retrieveData(
      PEGA_API.updatePage,
      this._bffInterprete.requestMapper[PEGA_API.updatePage]({
        refreshFor: actionData?.refreshFor,
        referencesToUpdate: data?.referencesToUpdate,
      }),
      actionData?.target
    );
  };

  handleFinishAssignment = async (evt, data) => {
    const actionData = data?.actionData;
    const referencesToUpdate = data?.referencesToUpdate;
    const actionId = data?.actionData?.actionName;

    console.log(
      '[TEST] handleFinishAssignment',
      '\n\nEVT:\n',
      utils.printObject(evt),
      evt,
      '\n\nDATA:\n',
      utils.printObject(data)
    );

    this.retrieveData(
      PEGA_API.nextPage,
      this._bffInterprete.requestMapper[PEGA_API.nextPage]({
        actionId: actionId || null,
        referencesToUpdate: referencesToUpdate,
      }),
      actionData?.target
    );
  };

  handleRunDataTransform = async (evt, data) => {
    const actionInfo = data?.actionData;
    const referencesToUpdate = data?.referencesToUpdate;
    const actionIdentifier =
      referencesToUpdate?.['GestioneProcesso.StepSuccessivo'] || actionInfo?.actionProcess?.dataTransform;

    if (actionIdentifier === 'RichiediContatto') {
      const refs = {
        AssignmentOrigine: this._bffInterprete._assignmentId,
      };

      const request = this._bffInterprete.requestMapper[PEGA_API.loadPage](ACTION_LOAD_REQUEST.create, {
        retrieveType: 'Richiesta Contatto',
        productType: 'UNICO',
        referencesToUpdate: refs,
      });
      this.retrieveData(PEGA_API.loadPage, request, actionInfo?.target);
      return;
    }
    const request = this._bffInterprete.requestMapper[PEGA_API.updatePage]({
      actionId: actionIdentifier,
      referencesToUpdate,
    });
    this.retrieveData(PEGA_API.updatePage, request, actionInfo?.target);
  };

  handleCloseContainer = () => {
    this.closeModal();
  };

  /**
   * Generates a list of actions based on the provided field and parent layout.
   *
   * @param {Object} field - The field object containing control and actionSets.
   * @param {Object} parentLayout - The parent layout object (not used in the current implementation).
   * @returns {ActionsList[]} - An array of objects, each containing a handler function and associated action
   *   data.
   */
  getActionsList = (field, parentLayout) => {
    const actionData = utilsPega.actions.getActionData(field);

    const referencesToUpdate = {
      ...utilsPega.actions.getReferencesToUpdate(field),
      ...this.caseData,
      ...field.referencesToUpdate,
    };

    // let hasFieldRefresh = false;
    /** @type {ActionsList[]} */
    const actionsList = [];

    const hasMajorAction = actionData.some((a) =>
      [
        utilsPega.actions.SUPPORTED_ACTIONS.LOCAL_ACTION,
        utilsPega.actions.SUPPORTED_ACTIONS.FINISH_ASSIGNMENT,
        utilsPega.actions.SUPPORTED_ACTIONS.PERFORM_ACTION,
      ].includes(a.action)
    );

    for (let i = 0; i < actionData.length; i++) {
      const currentAction = actionData[i];

      if (currentAction.action === utilsPega.actions.SUPPORTED_ACTIONS.RUN_DATA_TRANSFORM && hasMajorAction) {
        console.warn(
          "[Interprete] Azione 'runDataTransform' ignorata per evitare conflitto con un'altra azione principale."
        );
        continue;
      }

      switch (currentAction.action) {
        case utilsPega.actions.SUPPORTED_ACTIONS.LOCAL_ACTION:
          actionsList.push({
            handler: this.handleLocalAction,
            data: {
              actionData: currentAction.actionProcess,
              referencesToUpdate,
            },
          });
          break;

        case utilsPega.actions.SUPPORTED_ACTIONS.RUN_DATA_TRANSFORM:
          actionsList.push({
            handler: this.handleRunDataTransform,
            data: {
              actionData: currentAction,
              referencesToUpdate,
            },
          });
          break;

        case utilsPega.actions.SUPPORTED_ACTIONS.REFRESH:
          actionsList.push({
            handler: this.handlePostValue,
            data: {
              actionData: currentAction,
              referencesToUpdate,
            },
          });
          break;

        case utilsPega.actions.SUPPORTED_ACTIONS.FINISH_ASSIGNMENT:
        case utilsPega.actions.SUPPORTED_ACTIONS.PERFORM_ACTION:
          actionsList.push({
            handler: this.handleFinishAssignment,
            data: {
              actionData: currentAction.actionProcess,
              referencesToUpdate,
            },
          });
          break;

        case utilsPega.actions.SUPPORTED_ACTIONS.CLOSE_CONTAINER:
          actionsList.push({
            handler: this.handleCloseContainer,
            data: {
              actionData: currentAction.actionProcess,
              referencesToUpdate,
            },
          });
          break;

        case utilsPega.actions.SUPPORTED_ACTIONS.RUN_DATA_TRANSFORM:
          actionsList.push({
            handler: this.handleRunDataTransform,
            data: {
              actionData: actionData[i],
              referencesToUpdate,
            },
          });
          break;

        case utilsPega.actions.SUPPORTED_ACTIONS.CLOSE_CONTAINER:
          actionsList.push({
            handler: this.handleCloseContainer,
            data: {
              actionData: currentAction.actionProcess,
              referencesToUpdate,
            },
          });
          break;
        default:
          break;
      }
    }

    return actionsList;
  };

  /**
   * @param {PegaAPI} api
   * @param {any} request
   * @param {string} [target]
   */
  retrieveData(api, request, target) {
    this.isLoading = true;
    this.reloadVisible = api !== PEGA_API.loadPage;

    this._bffInterprete
      .makeRequest(api, request, target)
      .then((response) => {
        if (response?.pegaErrorMessages) {
          const errorMsg = response?.pegaErrorMessages?.[0];
          this.dispatchEvent(
            new CustomEvent('noresult', { bubbles: true, composed: true, detail: errorMsg })
          );
        }

        console.log('[DEBUG] retrieveData response:', {
          target: target,
          viewID: response?.pegaBodyResponse?.view?.viewID,
          viewName: response?.pegaBodyResponse?.view?.name,
          hasView: !!response?.pegaBodyResponse?.view,
          response: response
        });

        switch (target) {
          case 'modalDialog':
            this.modalView = response?.pegaBodyResponse?.view;
            this.showModal = true;
            break;

          default:
            console.log('[DEBUG] Setting new view:', response?.pegaBodyResponse?.view?.viewID);
            this.view = response?.pegaBodyResponse?.view || this.emptyView;
            break;
        }
      })
      .catch((error) => {
        this.handlePageError(error);
        if (!this.view) {
          this.view = this.emptyView;
        }
        console.error('Error retrieving data:', utils.printObject(error));
      })
      .finally(() => {
        this.isLoading = false;
      });
  }

  connectedCallback() {
    this.config.productType = this.request?.productType || '';
    this._bffInterprete = new BffInterprete(this.config);
    this.runLoadPage();
    registerListener('handleFieldChanged', this.handleFieldChanged, this);
    registerListener('handleFieldClicked', this.handleFieldClicked, this);
    registerListener('handlerRetryClicked', this.handlerRetryClicked, this);
  }

  disconnectedCallback() {
    unregisterListener('handleFieldChanged', this.handleFieldChanged, this);
    unregisterListener('handleFieldClicked', this.handleFieldClicked, this);
  }
}
