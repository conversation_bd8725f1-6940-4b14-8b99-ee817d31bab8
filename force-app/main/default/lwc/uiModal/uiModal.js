import { api, LightningElement } from "lwc";

export default class UiModal extends LightningElement {
  _modalContent;
  @api isOpen;
  @api closeModal;

  @api
  get modalContent() {
    return this._modalContent;
  }

  set modalContent(value) {    
    this._modalContent = value;
  }

  get modalLayoutGroups() {
    console.log('il modalContent groups è: ', JSON.stringify(this.modalContent.groups[0]));
    
    return this.modalContent.groups[0];
  }
}
