import { errors } from 'c/errorMessage';
import { utilsPega } from 'c/utilsPega';
const formState = {
  values: {}, // Qui salviamo i valori dei campi
  requiredFields: new Set(), // Qui salviamo i campi obbligatori
  touchedFields: new Set(), // Qui salviamo i campi con cui abbiamo interagito
  formControls: {}, // Qui salviamo i controlli del form
};

const setFieldValue = (fieldName, value, validationType, isRequired = false) => {
  formState.values[fieldName] = { value, validationType, isRequired };
  
  if (isRequired) {
    formState.requiredFields.add(fieldName);
  } else {
    formState.requiredFields.delete(fieldName);
    if (formState.values.hasOwnProperty(fieldName) && !patterns[validationType]) { //se presente validazione con pattern lascio il campo da controllare, quindi non lo elimino
      delete formState.values[fieldName];
    }
  }
};

const markFieldTouched = (fieldName) => {
  formState.touchedFields.add(fieldName);
};

const getFormState = () => {
  return formState;
};

const resetFormState = () => {
  formState.values = {};
  formState.requiredFields = new Set();
  formState.touchedFields = new Set();
  formState.formControls = {};
};

// Aggiunge un controllo al form
const addControlFormGroup = (controlId, control) => {
  formState.formControls[controlId] = control;
  console.log(`Controllo ${controlId} aggiunto al form`);
};

// Rimuove un controllo dal form
const removeControlFormGroup = (controlId) => {
  if (formState.formControls[controlId]) {
    delete formState.formControls[controlId];
    console.log(`Controllo ${controlId} rimosso dal form`);
  }
};

// Verifica se il form ha blocchi di invio
const hasSubmitBlockers = () => {
  for (const controlId in formState.formControls) {
    const control = formState.formControls[controlId];
    if (control.errors && control.errors.block) {
      return true;
    }
  }
  return false;
};

const isFormValid = () => {
  let isValid = true;
  let errorsList = {};
  let name,surname,cf;
  formState.requiredFields.forEach((field) => {
    const fieldData = formState.values[field];

    // Se il campo non esiste nei valori, salta
    if (!fieldData) {
      isValid = false;
      errorsList[field] = 'Campo non inizializzato.';
      return;
    }

    const { value, validationType } = fieldData;

    markFieldTouched(field);

    // Controllo più robusto sul valore
    if (value === undefined || value === null || value === '') {
      isValid = false;
      errorsList[field] = 'Questo campo è obbligatorio.';
      return;
    }

    const validationResult = checkPatternForType(value, validationType);
    if (!validationResult.isValid) {
      isValid = false;
      errorsList[field] = validationResult.errorMessage;
    }
  });

  Object.keys(formState.values).forEach((field) => {
    const { value, validationType } = formState.values[field];
    if(validationType === 'nameWithCF'){ //salvo il nome
      name = value;
    }
    if(validationType === 'surnameWithCF'){ //salvo il cognome
      surname = value;
    }
    if(validationType === 'taxCode'){ //salvo il codice fiscale
      cf = value;
    }
    if (value === '') {
      return;
    } else if (validationType) {
      const validationResult = checkPatternForType(value, validationType);
      if (!validationResult.isValid) {
        isValid = false;
        errorsList[field] = validationResult.errorMessage;
      }
    }
  });
  // Controllo sui blocchi di invio
  if (hasSubmitBlockers()) {
    isValid = false;
    errorsList['submitBlocker'] = "Ci sono blocchi che impediscono l'invio del form.";
  }

  //se presenti nome cognome e cf devo bloccare se non coerenti
  if (name && surname && cf) {
      isValid = isValid && utilsPega.customAttributes.checkNameSurnameOnFiscalCode(cf,name,'name') && 
                utilsPega.customAttributes.checkNameSurnameOnFiscalCode(cf,surname,'surname');
    }

  


  return { isValid, errorsList };
};

const checkPatternForType = (value, validationType) => {
  // Gestione sicura dei tipi di validazione mancanti
  if (!validationType || !patterns[validationType]) {
    return { isValid: true, errorMessage: '' };
  }

  // Utilizzo di String(value) per garantire che value sia una stringa
  const pattern = new RegExp(patterns[validationType]);
  let errorMessage = errors[validationType] || 'Formato non valido';

  // Calcolo isValid una sola volta per evitare inconsistenze
  const isValid = pattern.test(String(value));

  return {
    isValid: isValid,
    errorMessage: isValid ? '' : errorMessage,
  };
};

const patterns = {
  number: '^[0-9]+$',
  houseNumber: '^[0-9]+-?([a-zA-Z])*$',
  email: '^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$',
  mobilephone: '^[0-9]{10}$',
  taxCode: '^[a-zA-Z]{6}[0-9]{2}[a-zA-Z][0-9]{2}[a-zA-Z][0-9]{3}[a-zA-Z]$',
};

// Esporta le funzioni
export {
  addControlFormGroup,
  checkPatternForType,
  getFormState,
  hasSubmitBlockers,
  isFormValid,
  markFieldTouched,
  patterns,
  removeControlFormGroup,
  setFieldValue,
  resetFormState
};