/// <reference path="./utilsPegaText.jsdoc.js" />

/**
 * @param {string} str
 * @returns {TextCss | null}
 */
const getTextCss = (str) => {
  const textConfig = getTextConfig(str);
  if (textConfig) {
    const desktopCss = getCssFromProps(textConfig.desktop);
    const tabletCss = getCssFromProps(textConfig.tablet);
    const mobileCss = getCssFromProps(textConfig.mobile);
    return {
      desktopCss: desktopCss,
      tabletCss: tabletCss,
      mobileCss: mobileCss
    };
  }
  return null;
};

/**
 * @param {TextProps} props
 * @returns {CssElement}
 */
const getCssFromProps = (props) => {
  if (!props) {
    return {};
  } 

  const css = {
    color: `${props.color}`,
    "font-size": `${props.size}`,
    "font-family": `${props.fontFamily}`,
    "font-weight": `${props.weight}`,
    "text-align": `${props.alignment}`
  };

  if (props.transformation) {
    if (props.transformation.includes("text-decoration")) {
      const textDecoration = props.transformation.split("text-decoration: ")[1];
      css["text-decoration"] = textDecoration;
    } else if (props.transformation.includes("text-transform")) {
      const textTransform = props.transformation.split("text-transform: ")[1];
      css["text-transform"] = textTransform;
    } else {
      const fontStyle = props.transformation.split("font-style: ")[1];
      css["font-style"] = fontStyle;
    }
  }
  return css;
};

/**
 * @param {string} text
 * @returns {TextConfig | null}
 */
const getTextConfig = (text) => {
  if (
    text && (
      (text.includes('Text') || text.includes('TEXT')) ||
      (text.includes(' WEB ') || text.includes(' web ') ||
      (text.includes('HTML') || text.includes('html')))
    )
  ) {
    const textWebConfig = text.split("WEB ")[1];
    if (textWebConfig) {
      const text = textWebConfig.split(" ");
      const desktopConfig = getTextProps(text[0]);
      const tabletConfig = getTextProps(text[1]);
      const mobileConfig = getTextProps(text[2]);

      const textConfig = {
        desktop: desktopConfig,
        tablet: tabletConfig,
        mobile: mobileConfig
      };
      return textConfig;
    }
  }
  return null;
};

/**
 * @param {string} config
 * @returns {TextProps}
 */
const getTextProps = (config) => {
  if(!config) return;
  const color = getTextColor(config.slice(0, 2));
  const weight = getTextWeight(config.charAt(2));
  let size = "0";
  let transformation = "";

  if (!is_numeric(config[config.length - 1])) {
    transformation = getTextTransformation(config.charAt(config.length - 1));
  }

  if (is_numeric(config[3])) {
    size = getFontSize(config, 3);
  } else {
    transformation = getTextTransformation(config.charAt(3));
    size = getFontSize(config, 4);
  }

  const alignment = getTextAlignment(config);

  return {
    color: color,
    ...weight,
    size: size,
    transformation: transformation,
    alignment: alignment,
  };
};

/**
 * @param {string} str
 * @returns {boolean}
 */
const is_numeric = (str) => {
  return /^\d+$/.test(str);
};

/**
 * @param {string} config
 * @returns {string}
 */
const getTextColor = (config) => {
  return getColor(config);
};

/**
 * @param {string} weight
 * @returns {string}
 */
const getTextWeight = (weight) => {
  switch (weight) {
    case "E":
      return {weight: "100", fontFamily: 'Unipol'};
    case "L":
      return {weight: "Normal", fontFamily: 'Unipol'};
    case "M":
      return {weight: "500", fontFamily: 'Unipol Medium'};
    case "B":
      return {weight: "Bold", fontFamily: 'Unipol Bold'};
    default:
      return {weight: "Normal", fontFamily: 'Unipol'};
  }
};

/**
 * @param {string} str
 * @param {number} startingIndex
 * @returns {string}
 */
const getFontSize = (str, startingIndex) => {
  let size = "";
  for (let i = startingIndex; i < str.length; i++) {
    if (is_numeric(str[i])) {
      size = `${size}${str[i]}`;
    } else break;
  }
  return `${size}px`;
};

/**
 * @param {string} str
 * @returns {string}
 */
const getTextAlignment = (str) => {
  const lastChar = str.charAt(str.length - 1);
  const alignment = is_numeric(lastChar) ? "" : lastChar;

  switch (alignment) {
    case "C":
      return "center";
    case "R":
      return "right";
    default:
      return "left";
  }
};

/**
 * @param {string} transformation
 * @returns {string}
 */
const getTextTransformation = (transformation) => {
  switch (transformation) {
    case "S":
      return "text-decoration: line-through";
    case "N":
      return "text-decoration: underline";
    case "U":
      return "text-transform: uppercase";
    case "I":
      return "font-style: italic";
    default:
      return "";
  }
};

const COLORS = {
  RE: "#C4151C",
  WH: "#FFFFFF",
  WO: "#FFFFFFCC",
  GD: "#5C5C5C",
  GR: "#9B9B9B",
  GL: "#F1F1F1",
  BD: "#193A56",
  BB: "#1F5B8E",
  BL: "#5393BC",
  BA: "#193A57E6",
  BC: "#0F3250",
  CB: "#E2F0F9",
  GN: "#CCCCCC",
  GS: "#747474",
  DEFAULT: "#9B9B9B"
};

const getColor = (key) => {
  let esito = COLORS["DEFAULT"];
  if (Object.keys(COLORS).includes(key)) esito = COLORS[key];
  return esito;
};

export const utilsPegaText = { getTextCss, getColor };
