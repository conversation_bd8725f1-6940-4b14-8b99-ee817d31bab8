import { utilsPegaText } from 'c/utilsPegaText';

export const utils = {
  printObject,
  generateKey,
  decodeHTML,
  generateRandomRequestId,
  apiRequest,
  getClassFromFormat,
  getStyleStringFromObj,
  getCustomAttributeFromFieldUnsensitive,
  getFirstFieldInGroupsByType,
  getFirstIconInGroupsByResource,
  getFirstCaptionByControlFormat,
  captionToValue,
  getFirstLayoutInGroupsByGroupFormat,
  getFirstCaptionInGroupsId,
  fieldToValue,
  formatCurrency,
  getFirstFieldInGroupsByFieldId,
  getEachCaptionInGroups,
  getFirstIntegerByFieldId,
  getFirstCurrencyByFieldId,
  getCaptionByCaptionFor,
  getParagraphByParagraphId,
  getEachVisibleFieldInGroups,
  getFirstFieldInGroupsByComponentId,
  getFirstViewInGroupsByViewId,
  getAllFieldInGroupsNotCloned,
  getAllLayoutInGroupsByGroupFormat,
  formatDateByField
};

function printObject(obj) {
  // if (obj) return JSON.stringify(obj);
  // return undefined;

  try {
    return JSON.stringify(obj, null, 2);
  } catch (error) {
    console.error('[TEST] Invalid JSON object', error);
    return null;
  }
}

function generateKey(prefix = 'k') {
  return `${prefix}_${Math.random().toString(36).substr(2, 9)}`;
}

function decodeHTML(value) {
  if (value) {
    if (value.indexOf('&') !== -1) {
      const doc = new DOMParser().parseFromString(value, 'text/html');
      return doc.documentElement.textContent;
    }
  }
  return value;
}

function generateRandomRequestId() {
  this._groups;
  return '2'.concat(new Date().getTime().toString()).concat(Math.round(Math.random() * 90 + 10).toString());
}

async function apiRequest(method, url, headers, body) {
  if (method === 'GET') {
    console.log(method);
    console.log(url);
    console.log(JSON.stringify(headers));
  }

  try {
    const request = await fetch(url, {
      method,
      headers,
      body: body ? JSON.stringify(body) : undefined,
      redirect: 'follow',
    });

    // Handle HTTP error case
    if (!request.ok) {
      return {
        error: `Error API: ${request.status} - ${request.statusText}`,
        status: request.status,
      };
    }

    const result = await request.text();

    // If the response is empty, it returns an error
    if (!result) {
      return { error: 'The API response is empty or undefined' };
    }

    try {
      const parsedResult = JSON.parse(result);
      if (!parsedResult || Object.keys(parsedResult).length === 0) {
        return { error: 'The JSON API response is empty' };
      }

      return parsedResult;
    } catch (error) {
      return { error: 'Invalid JSON object' };
    }
  } catch (error) {
    return { error: `Error in API call: ${error.message}` };
  }
}

function getClassFromFormat(labelFormat) {
  const supportedAlign = ['Default', 'Left', 'Right', 'Center', 'default', 'center', 'left', 'right'];
  let align;
  let cleanLabel = labelFormat;
  let match;

  supportedAlign.forEach((a) => {
    if (labelFormat.includes(a)) {
      match = a;
      align = `text-align-${a}`;
    }
  });
  if (match) {
    cleanLabel = labelFormat.replace(`${match}`, '').trim();
  }

  // replace spaces with "-"
  // remove ( ) for css class mapping
  cleanLabel = cleanLabel.replace(/\(+(.*?)\)+/g, '$1').replace(/\s+/gi, '-');
  return align ? `${cleanLabel} ${align}` : cleanLabel;
}

function getStyleStringFromObj(style) {
  if (style)
    return Object.entries(style)
      .reduce((acc, [key, value]) => {
        return `${acc}${key}: ${value}; `;
      }, '')
      .trim();
  else return '';
}

function getCustomAttributeFromFieldUnsensitive(field, attributeName, defaultValue = '') {
  let esito = defaultValue;
  if (field.customAttributes) {
    for (const key of Object.keys(field.customAttributes)) {
      if (key.toLowerCase() === attributeName.toLowerCase()) {
        esito = field.customAttributes[key];
        break;
      }
    }
  }
  return esito;
}

function _retrieveFirst(groups, condition, clone = true) {
  let esito = undefined;

  if (groups) {
    for (const group of groups) {
      if (condition(group)) {
        esito = clone ? JSON.parse(JSON.stringify(group)) : group;
      } else if (group.layout) {
        esito = _retrieveFirst(group.layout.groups, condition, clone);
        if (!esito && group.layout.rows) {
          for (const row of group.layout.rows) {
            esito = _retrieveFirst(row.groups, condition, clone);
            if (esito) break;
          }
        }
      } else if (group.view) esito = _retrieveFirst(group.view.groups, condition, clone);

      if (esito) break;
    }
  }

  return esito;
}

function _retrieveAll(groups, condition, clone = true) {
  const esito = [];

  if (groups) {
    for (const group of groups) {
      if (condition(group)) esito.push(clone ? JSON.parse(JSON.stringify(group)) : group);
      else if (group.layout) {
        esito.push(..._retrieveAll(group.layout.groups, condition, clone));
        if (group.layout.rows) {
          for (const row of group.layout.rows) {
            esito.push(..._retrieveAll(row.groups, condition, clone));
          }
        }
      } else if (group.view) esito.push(..._retrieveAll(group.view.groups, condition, clone));
    }
  }

  return esito;
}

function getFirstLayoutInGroupsByGroupFormat(groups, groupFormat) {
  return _retrieveFirst(groups, (group) => {
    return group?.layout?.groupFormat === groupFormat;
  })?.layout;
}

function getAllLayoutInGroupsByGroupFormat(groups, groupFormat) {
  return _retrieveAll(groups, (group) => group?.layout?.groupFormat === groupFormat).map(
    (group) => group.layout
  );
}

function getFirstCaptionInGroupsId(groups) {
  return _retrieveFirst(groups, (group) => {
    return group?.caption !== undefined;
  })?.caption;
}

function fieldToValue(field) {
  return field?.value || '';
}

function formatCurrency(value) {
  if (!value) return '0,00';
  return parseFloat(value).toFixed(2).replace('.', ',');
}

function getCaptionByCaptionFor(groups, captionFor) {
  return _retrieveFirst(groups, (group) => {
    return group?.caption?.captionFor === captionFor;
  })?.caption;
}

function getParagraphByParagraphId(groups, paragraphId) {
  return _retrieveFirst(groups, (group) => {
    return group?.paragraph?.paragraphID === paragraphId;
  })?.paragraph;
}

function getFirstIntegerByFieldId(groups, fieldId) {
  const field = _retrieveFirst(groups, (group) => {
    return group?.field?.control?.type === 'pxInteger' && group?.field?.fieldID === fieldId;
  })?.field;

  if (field) {
    const value = field?.value || '';
    const symbol = field.control?.modes?.find((mode) => mode.modeType === 'readOnly')?.symbolValue || '';
    return { value, symbol };
  }

  return null;
}

function getFirstCurrencyByFieldId(groups, fieldId) {
  const field = _retrieveFirst(groups, (group) => {
    return group?.field?.control?.type === 'pxCurrency' && group?.field?.fieldID === fieldId;
  })?.field;

  if (field) {
    const value = field?.value || '';
    const symbol = field.control?.modes?.find((mode) => mode.modeType === 'readOnly')?.symbolValue || '';
    return { value, symbol };
  }

  return null;
}

function getFirstFieldInGroupsByFieldId(groups, fieldType, fieldId) {
  return _retrieveFirst(groups, (group) => {
    let esito = group?.field?.control?.type === fieldType && group?.field?.id === fieldId;
    return esito;
  })?.field;
}

function getFirstFieldInGroupsByType(groups, fieldType, visible) {
  return _retrieveFirst(groups, (group) => {
    let esito = group?.field?.control?.type === fieldType;
    if (visible !== undefined) esito &&= group?.field?.visible === visible;
    return esito;
  })?.field;
}

function getFirstIconInGroupsByResource(groups, resourceName) {
  return _retrieveFirst(
    groups,
    (group) =>
      group?.field?.control?.type === 'pxIcon' && group?.field?.customAttributes?.resource === resourceName
  )?.field;
}

function getFirstCaptionByControlFormat(groups, controlFormat, controllaVisibilita = false) {
  return _retrieveFirst(groups, (group) => {
    let esito = false;
    const format = group?.caption?.control?.format;
    if (format) {
      esito = format.split('STYLE')[0].trim() === controlFormat;
      if (esito && controllaVisibilita) esito = group?.caption?.visible || false;
    }
    return esito;
  })?.caption;
}

function captionToValue(caption) {
  const esito = {};

  if (caption && caption.visible) {
    esito['value'] = this.decodeHTML(caption?.value);
    const textCss = utilsPegaText.getTextCss(caption.control.format);
    if (textCss) esito['textCss'] = textCss;
  }

  return esito;
}

function getEachCaptionInGroups(groups) {
  return _retrieveAll(groups, (group) => !!group.caption)?.map((group) => group.caption);
}

function getEachVisibleFieldInGroups(groups) {
  return _retrieveAll(groups, (group) => group?.field?.visible).map((group) => group.field);
}

function getFirstFieldInGroupsByComponentId(groups, componentId) {
  return _retrieveFirst(groups, (group) => group?.field?.customAttributes?.componentID === componentId)
    ?.field;
}

function getFirstViewInGroupsByViewId(groups, viewId) {
  return _retrieveFirst(groups, (group) => group?.view?.viewID === viewId)?.view;
}

function getAllFieldInGroupsNotCloned(groups) {
  return _retrieveAll(groups, (group) => !!group?.field, false).map((group) => group.field);
}

function formatDateByField(input, field) {
  // Rimuove caratteri non numerici
  const clean = input.replace(/\D/g, '');
  const outputFormat = field?.customAttributes?.dateFormat || 'yyyy-MM-dd';
  if (clean.length !== 8) {
    throw new Error('Data non valida. Atteso formato aaaaMMdd, aaaa-MM-dd o simile.');
  }

  const year = clean.slice(0, 4);
  const month = clean.slice(4, 6);
  const day = clean.slice(6, 8);

  switch (outputFormat) {
    case 'dd/MM/yyyy':
      return `${day}/${month}/${year}`;
    case 'dd-MM-yyyy':
      return `${day}-${month}-${year}`;
    case 'yyyy/MM/dd':
      return `${year}/${month}/${day}`;
    case 'yyyy-MM-dd':
      return `${year}-${month}-${day}`;
    default:
      throw new Error(`Formato di output non supportato: ${outputFormat}`);
  }
}
