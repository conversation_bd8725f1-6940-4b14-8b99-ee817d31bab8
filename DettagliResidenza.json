{"pegaBodyResponse": {"view": {"reference": "", "validationMessages": "", "viewID": "DettagliResidenza", "visible": true, "titleFormat": "", "name": "DettagliResidenza", "appliesTo": "UG-Ins-PU-Work-Quotazione", "groups": [{"view": {"reference": "", "validationMessages": "", "viewID": "AnalyticsHidden", "visible": true, "titleFormat": "", "name": "AnalyticsHidden", "appliesTo": "UG-Ins-PU-Work-Quotazione", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "pyTemplateInputBox", "labelFormat": "Standard", "disabled": false, "testID": "202212131640340513751", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateInputBox", "customAttributes": {"customType": "analyticsPageFields", "funnel_name": "Prodotto Unico", "page_name": "unico:residenza", "proposal_source": "web", "user_fiscal_code": "", "funnel_type": "nuovo", "postsale_contract_operation_type": ""}}}], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}}, {"view": {"reference": "", "validationMessages": "", "viewID": "Intestazione", "visible": true, "titleFormat": "", "name": "Intestazione", "appliesTo": "UG-Ins-PU-Work-Quotazione", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "pyTemplateInputBox", "labelFormat": "Standard", "disabled": false, "testID": "202212131640340513751", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateInputBox", "customAttributes": {"customType": "Header", "background": "Secondary"}}}, {"caption": {"columnImportance": "", "visible": true, "captionFor": "", "control": {"format": "Standard", "testID": "202301131218420210633"}, "value": "La tua casa", "testID": "202301131218420210633"}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore", "iconSource": "image", "iconImage": "webwb/pymenuleftarrow.png"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "controlFormat": "Standard", "formatType": "text", "showReadOnlyValidation": "false", "iconSource": "image", "iconImage": "webwb/pymenuleftarrow.png"}], "actionSets": [{"actions": [{"action": "takeAction", "actionProcess": {"actionName": "Indietro"}}], "events": [{"event": "click"}]}], "type": "pxIcon"}, "label": "Image", "type": "Text", "required": false, "validateAs": "", "reference": "pyTemplateGeneric", "labelFormat": "Standard", "disabled": false, "testID": "202212141106470503509", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateGeneric", "customAttributes": {"componentID": "_back", "size": "S", "resource": "arrowLeft", "type": "icon"}, "showLabel": false}}], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}}, {"view": {"reference": "", "validationMessages": "", "viewID": "BarraDiAvanzamento", "visible": true, "titleFormat": "", "name": "BarraDiAvanzamento", "appliesTo": "UG-Ins-PU-Work-Quotazione", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "pyTemplateInputBox", "labelFormat": "Standard", "disabled": false, "testID": "202212131640340513751", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateInputBox", "customAttributes": {"currentStep": "0", "labelFormatDeselected": "TEXT APP GRL13 WEB GRL14 GRL14 GRL14", "customType": "Stepper", "labelFormatPreviousStep": "TEXT APP BDL13 WEB BDL14 BDL14 BDL14", "progress": "29", "style": "StepperPU", "labelFormatCurrentStep": "TEXT APP BDM13 WEB BDM14 BDM14 BDM14", "labelsStepper": "Preventivo|Carrello|Acquisto"}}}], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"view": {"reference": "Ambito", "validationMessages": "", "viewID": "DettagliResidenza", "visible": true, "titleFormat": "", "name": "DettagliResidenza", "appliesTo": "UG-Ins-PU-Data-Ambito", "groups": [{"view": {"reference": "Ambito.Bene", "validationMessages": "", "viewID": "DettagliResidenza", "visible": true, "titleFormat": "", "name": "DettagliResidenza", "appliesTo": "UG-Ins-PU-Data-Bene", "groups": [{"view": {"reference": "Ambito.Bene.Casa", "validationMessages": "", "viewID": "DettagliResidenza", "visible": true, "titleFormat": "", "name": "DettagliResidenza", "appliesTo": "UG-Ins-PU-Data-Casa", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "", "control": {"format": "Text APP BDM16 WEB BDB20C BDB20C BDB16C", "testID": "202302131222180692405"}, "value": "Hai la residenza in questa abitazione?", "testID": "202302131222180692405"}}, {"caption": {"columnImportance": "", "visible": false, "captionFor": "", "control": {"format": "Text APP BDM16 WEB BDB20C BDB20C BDB16C", "testID": "202302131222180692273"}, "value": "Il membro del nucleo è residente in questa casa?", "testID": "202302131222180692273"}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"orientation": "vertical", "listSource": "locallist", "textAlign": "Left", "wrapAfter": 3, "tooltip": "", "minChars": "", "modeType": "editable", "controlFormat": "", "options": [{"value": "Si", "key": "Si"}, {"value": "No", "key": "No"}], "specifySize": "auto", "formatType": "text", "obfuscated": false, "maxChars": ""}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false, "showReadOnlyValidation": "false"}], "actionSets": [{"actions": [{"action": "refresh", "refreshFor": "RGV0dGFnbGlSZXNpZGVuemFVRy1JbnMtUFUtRGF0YS1DYXNhLkRpbW9yYVJlc2lkdWFsZTIwMjMwMTAyMTUxMjMwMDcwMzgz"}], "events": [{"event": "change"}]}], "type": "pxRadioButtons"}, "label": "", "type": "Text", "required": true, "validateAs": "", "reference": "Ambito.Bene.Casa.DimoraResiduale", "labelFormat": "", "disabled": false, "testID": "20230102151230070383", "value": "No", "maxLength": 0, "expectedLength": "", "fieldID": "DimoraResiduale", "customAttributes": {"style": "FlatCardInverted"}}}], "groupFormat": "Col TL TC 32 16 16 16", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}}], "title": ""}}], "title": ""}}, {"view": {"reference": "Ambito", "validationMessages": "", "viewID": "IndirizzoDiResidenzaAssicurato", "visible": true, "titleFormat": "", "name": "IndirizzoDiResidenzaAssicurato", "appliesTo": "UG-Ins-PU-Data-Ambito", "groups": [{"view": {"reference": "Ambito.Proprietario", "validationMessages": "", "viewID": "IndirizzoDiResidenzaAssicurato", "visible": true, "titleFormat": "h2", "name": "IndirizzoDiResidenzaAssicurato", "appliesTo": "UG-Ins-PU-Data-Persona", "groups": [{"view": {"reference": "Ambito.Proprietario.Residenza", "validationMessages": "", "viewID": "IndirizzoResidenza", "visible": true, "titleFormat": "h2", "name": "IndirizzoResidenza", "appliesTo": "UG-Ins-PU-Data-Indirizzo", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [{"actions": [{"action": "finishAssignment"}], "events": [{"event": "change"}]}, {"actions": [{"action": "refresh", "refreshFor": "SW5kaXJpenpvUmVzaWRlbnphVUctSW5zLVBVLURhdGEtSW5kaXJpenpvLnB5VGVtcGxhdGVJbnB1dEJveDIwMjMwMTAzMTEyMjAxMDIzNDU2MQ%3D%3D"}], "events": [{"event": "focus"}]}], "type": "pxHidden"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "Ambito.Proprietario.Residenza.pyTemplateInputBox", "labelFormat": "Standard", "disabled": false, "testID": "202301031122010234561", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateInputBox", "customAttributes": {"labelAutocomplete": "Indirizzo di residenza", "customType": "Address Autocomplete", "GestioneProcesso.MostraBoxResidenza": "true", "GestioneProcesso.StepSuccessivo_change": "Conferma", "GestioneProcesso.StepSuccessivo": "ModificaIndirizzo", "width": "Responsive", "styleLabelAutocomplete": "TEXT APP GDM16 WEB BAB16 BAB16 BAB16", "placeholder": "es: Via Roma 21, Milano 20156", "required": "true", "paddingApp": "0 16 0 16"}}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": true, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "controlFormat": "Full Cancel button", "formatType": "text", "showReadOnlyValidation": "false"}], "actionSets": [{"actions": [{"action": "runDataTransform"}, {"action": "finishAssignment"}], "events": [{"event": "click"}]}], "type": "px<PERSON><PERSON><PERSON>", "label": "CONFERMA"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "Ambito.Proprietario.Residenza.pyTemplateButton", "labelFormat": "TEXT APP WHB16 WEB WHB18 WHB18 WHB18", "disabled": false, "testID": "202303301750300182237", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateButton", "customAttributes": {"submitButton": "formContainer", "componentID": "Conferma civico"}, "showLabel": true}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"view": {"reference": "Ambito.Proprietario.Residenza", "validationMessages": "", "viewID": "InserimentoManualeIndirizzo", "visible": true, "titleFormat": "", "name": "InserimentoManualeIndirizzo", "appliesTo": "UG-Ins-PU-Data-Indirizzo", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "", "control": {"format": "TEXT APP GDL16 WEB BDL20C BDL20C BDL16C", "testID": "202303311115420893262"}, "value": "Inserisci l'indirizzo completo con il numero civico", "testID": "202303311115420893262"}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": true, "readOnly": false, "control": {"modes": [{"textAlign": "Left", "tooltip": "", "minChars": "", "modeType": "editable", "controlFormat": "Standard", "specifySize": "auto", "formatType": "none", "maxChars": ""}, {"tooltip": "", "modeType": "readOnly", "formatType": "none", "showReadOnlyValidation": "false"}], "actionSets": [], "type": "pxTextInput"}, "label": "Via / Piazza", "type": "Text", "required": true, "validateAs": "", "reference": "Ambito.Proprietario.Residenza.NomeStrada", "labelFormat": "TEXT APP GDB16 WEB BAB16 BAB16 BAB16", "disabled": false, "testID": "202302071254470912426", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "NomeStrada"}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": true, "readOnly": false, "control": {"modes": [{"textAlign": "Left", "tooltip": "", "minChars": "", "modeType": "editable", "controlFormat": "Standard", "specifySize": "auto", "formatType": "none", "maxChars": ""}, {"tooltip": "", "modeType": "readOnly", "formatType": "none", "showReadOnlyValidation": "false"}], "actionSets": [], "type": "pxTextInput"}, "label": "N° Civico", "type": "Text", "required": true, "validateAs": "", "reference": "Ambito.Proprietario.Residenza.NumeroCivico", "labelFormat": "TEXT APP GDB16 WEB BAB16 BAB16 BAB16", "disabled": false, "testID": "202302071254470913320", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "NumeroCivico", "customAttributes": {"validation": "houseNumber"}}}], "groupFormat": "Inline grid 70 30", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": true, "readOnly": false, "control": {"modes": [{"dataPagePrompt": "Provincia", "groupOrder": "asc", "listSource": "datapage", "textAlign": "Left", "tooltip": "", "enableGrouping": false, "groupBy": "", "minChars": "", "dataPageParams": [], "modeType": "editable", "dataPageValue": "SiglaProvincia", "controlFormat": "Standard", "dataPageTooltip": "", "loadMode": "auto", "options": [{"value": "Agrigento", "key": "AG"}, {"value": "Alessandria", "key": "AL"}, {"value": "Ancona", "key": "AN"}, {"value": "Arezzo", "key": "AR"}, {"value": "<PERSON><PERSON><PERSON>", "key": "AP"}, {"value": "<PERSON><PERSON>", "key": "AT"}, {"value": "<PERSON><PERSON><PERSON>", "key": "AV"}, {"value": "Bari", "key": "BA"}, {"value": "Barletta-Andria-Trani", "key": "BT"}, {"value": "<PERSON><PERSON>", "key": "BL"}, {"value": "Benevento", "key": "BN"}, {"value": "Bergamo", "key": "BG"}, {"value": "Biella", "key": "BI"}, {"value": "Bologna", "key": "BO"}, {"value": "Bolzano/Bozen", "key": "BZ"}, {"value": "Brescia", "key": "BS"}, {"value": "Brindisi", "key": "BR"}, {"value": "Cagliari", "key": "CA"}, {"value": "Caltanissetta", "key": "CL"}, {"value": "Campobasso", "key": "CB"}, {"value": "Caserta", "key": "CE"}, {"value": "Catania", "key": "CT"}, {"value": "Cat<PERSON>ro", "key": "CZ"}, {"value": "Chieti", "key": "CH"}, {"value": "Como", "key": "CO"}, {"value": "Cosenza", "key": "CS"}, {"value": "Cremona", "key": "CR"}, {"value": "Crotone", "key": "KR"}, {"value": "Cuneo", "key": "CN"}, {"value": "<PERSON><PERSON>", "key": "EN"}, {"value": "Fermo", "key": "FM"}, {"value": "<PERSON><PERSON><PERSON>", "key": "FE"}, {"value": "Firenze", "key": "FI"}, {"value": "Foggia", "key": "FG"}, {"value": "Forlì-Cesena", "key": "FC"}, {"value": "Frosinone", "key": "FR"}, {"value": "<PERSON><PERSON>", "key": "GE"}, {"value": "Gorizia", "key": "GO"}, {"value": "Grosseto", "key": "GR"}, {"value": "Imperia", "key": "IM"}, {"value": "Isernia", "key": "IS"}, {"value": "L'Aquila", "key": "AQ"}, {"value": "La Spezia", "key": "SP"}, {"value": "Latina", "key": "LT"}, {"value": "Lecce", "key": "LE"}, {"value": "<PERSON><PERSON>", "key": "LC"}, {"value": "Livorno", "key": "LI"}, {"value": "<PERSON><PERSON>", "key": "LO"}, {"value": "Lucca", "key": "LU"}, {"value": "Macerata", "key": "MC"}, {"value": "<PERSON><PERSON><PERSON>", "key": "MN"}, {"value": "Massa-Carrara", "key": "MS"}, {"value": "<PERSON><PERSON>", "key": "MT"}, {"value": "Messina", "key": "ME"}, {"value": "Milano", "key": "MI"}, {"value": "Modena", "key": "MO"}, {"value": "Monza e della Brianza", "key": "MB"}, {"value": "Napoli", "key": "NA"}, {"value": "Novara", "key": "NO"}, {"value": "<PERSON><PERSON><PERSON>", "key": "NU"}, {"value": "Oristano", "key": "OR"}, {"value": "<PERSON><PERSON><PERSON>", "key": "PD"}, {"value": "Palermo", "key": "PA"}, {"value": "Parma", "key": "PR"}, {"value": "Pavia", "key": "PV"}, {"value": "Perugia", "key": "PG"}, {"value": "Pesaro e Urbino", "key": "PU"}, {"value": "Pescara", "key": "PE"}, {"value": "Piacenza", "key": "PC"}, {"value": "Pisa", "key": "PI"}, {"value": "Pistoia", "key": "PT"}, {"value": "Pordenone", "key": "PN"}, {"value": "Potenza", "key": "PZ"}, {"value": "Prato", "key": "PO"}, {"value": "<PERSON><PERSON><PERSON>", "key": "RG"}, {"value": "<PERSON><PERSON>", "key": "RA"}, {"value": "Reggio Calabria", "key": "RC"}, {"value": "Reggio nell'Emilia", "key": "RE"}, {"value": "Riet<PERSON>", "key": "RI"}, {"value": "<PERSON><PERSON><PERSON>", "key": "RN"}, {"value": "Roma", "key": "RM"}, {"value": "Rovigo", "key": "RO"}, {"value": "Salerno", "key": "SA"}, {"value": "Sassari", "key": "SS"}, {"value": "<PERSON><PERSON><PERSON>", "key": "SV"}, {"value": "Siena", "key": "SI"}, {"value": "Sir<PERSON><PERSON>", "key": "SR"}, {"value": "Sondr<PERSON>", "key": "SO"}, {"value": "Sud Sardegna", "key": "SU"}, {"value": "<PERSON><PERSON>", "key": "TA"}, {"value": "Teramo", "key": "TE"}, {"value": "<PERSON><PERSON><PERSON>", "key": "TR"}, {"value": "Torino", "key": "TO"}, {"value": "<PERSON><PERSON><PERSON>", "key": "TP"}, {"value": "Trento", "key": "TN"}, {"value": "Treviso", "key": "TV"}, {"value": "Trieste", "key": "TS"}, {"value": "Udine", "key": "UD"}, {"value": "Valle d'Aosta/Vallée d'Aoste", "key": "AO"}, {"value": "<PERSON><PERSON><PERSON>", "key": "VA"}, {"value": "Venezia", "key": "VE"}, {"value": "Verbano-Cusio-Ossola", "key": "VB"}, {"value": "<PERSON><PERSON><PERSON><PERSON>", "key": "VC"}, {"value": "Verona", "key": "VR"}, {"value": "<PERSON><PERSON><PERSON>", "key": "VV"}, {"value": "Vicenza", "key": "VI"}, {"value": "Viterbo", "key": "VT"}], "dataPageID": "D_RecuperaListaProvince", "specifySize": "auto", "formatType": "none", "placeholder": "Seleziona", "maxChars": ""}, {"tooltip": "", "modeType": "readOnly", "formatType": "none", "showReadOnlyValidation": "false"}], "actionSets": [{"actions": [{"action": "postValue"}, {"action": "refresh", "refreshFor": "SW5zZXJpbWVudG9NYW51YWxlSW5kaXJpenpvVUctSW5zLVBVLURhdGEtSW5kaXJpenpvLlByb3ZpbmNpYTIwMjMwMjA3MTI1NDQ3MDkxNTgxMA%3D%3D"}], "events": [{"event": "change"}]}], "type": "pxDropdown"}, "label": "Provincia", "type": "Text", "required": true, "validateAs": "", "reference": "Ambito.Proprietario.Residenza.Provincia", "labelFormat": "TEXT APP GDB16 WEB BAB16 BAB16 BAB16", "disabled": false, "testID": "202302071254470915810", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "Provincia", "customAttributes": {"GestioneProcesso.MostraBoxIndirizzoImmobile": "false", "GestioneProcesso.MostraBoxResidenza": "false", "GestioneProcesso.StepSuccessivo": "ModificaIndirizzo"}}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": true, "readOnly": false, "control": {"modes": [{"dataPagePrompt": "<PERSON><PERSON><PERSON>", "groupOrder": "asc", "listSource": "datapage", "textAlign": "Left", "tooltip": "", "enableGrouping": false, "groupBy": "", "minChars": "", "dataPageParams": [{"valueReference": {"reference": ".Provincia", "lastSavedValue": ""}, "name": "CodProvincia"}], "modeType": "editable", "dataPageValue": "<PERSON><PERSON><PERSON>", "controlFormat": "Standard", "dataPageTooltip": "", "loadMode": "auto", "options": [], "dataPageID": "D_RecuperaComuneDaProvincia", "specifySize": "auto", "formatType": "none", "placeholder": "Seleziona", "maxChars": ""}, {"tooltip": "", "modeType": "readOnly", "formatType": "none", "showReadOnlyValidation": "false"}], "actionSets": [{"actions": [{"action": "postValue"}], "events": [{"event": "change"}]}], "type": "pxDropdown"}, "label": "<PERSON><PERSON><PERSON>", "type": "Text", "required": true, "validateAs": "", "reference": "Ambito.Proprietario.Residenza.Comune", "labelFormat": "TEXT APP GDB16 WEB BAB16 BAB16 BAB16", "disabled": false, "testID": "202302071254470915803", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "<PERSON><PERSON><PERSON>"}}, {"field": {"visible": false, "validateAs": "", "disabled": false, "fieldID": "<PERSON><PERSON><PERSON>"}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": true, "readOnly": false, "control": {"modes": [{"textAlign": "Left", "tooltip": "", "minChars": "", "modeType": "editable", "controlFormat": "Standard", "specifySize": "auto", "formatType": "none", "maxChars": "5"}, {"tooltip": "", "modeType": "readOnly", "formatType": "none", "showReadOnlyValidation": "false"}], "actionSets": [], "type": "pxTextInput"}, "label": "CAP", "type": "Text", "required": true, "validateAs": "", "reference": "Ambito.Proprietario.Residenza.Cap", "labelFormat": "TEXT APP GDB16 WEB BAB16 BAB16 BAB16", "disabled": false, "testID": "202302071254470915927", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "Cap", "customAttributes": {"validation": "number"}}}], "groupFormat": "Inline grid 70 30", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": true, "readOnly": false, "control": {"modes": [{"listSource": "locallist", "textAlign": "Left", "tooltip": "", "minChars": "", "modeType": "editable", "controlFormat": "Standard", "options": [{"value": "Italia", "key": "Italia"}, {"value": "San Marino", "key": "San Marino"}, {"value": "Città del Vaticano", "key": "Città del Vaticano"}, {"value": "Altri Stati", "key": "Altri Stati"}], "specifySize": "auto", "formatType": "none", "placeholder": "Seleziona", "maxChars": ""}, {"tooltip": "", "modeType": "readOnly", "formatType": "none", "showReadOnlyValidation": "false"}], "actionSets": [{"actions": [{"action": "postValue"}, {"action": "refresh", "refreshFor": "SW5zZXJpbWVudG9NYW51YWxlSW5kaXJpenpvVUctSW5zLVBVLURhdGEtSW5kaXJpenpvLlN0YXRvMjAyMzAyMDcxMjU0NDcwOTE2MjY4"}], "events": [{"event": "change"}]}], "type": "pxDropdown"}, "label": "Stato", "type": "Text", "required": true, "validateAs": "", "reference": "Ambito.Proprietario.Residenza.Stato", "labelFormat": "TEXT APP GDB16 WEB BAB16 BAB16 BAB16", "disabled": false, "testID": "202302071254470916268", "value": "Italia", "maxLength": 0, "expectedLength": "", "fieldID": "Stato", "customAttributes": {"GestioneProcesso.MostraBoxIndirizzoImmobile": "false", "GestioneProcesso.MostraBoxResidenza": "false", "GestioneProcesso.StepSuccessivo": "ModificaIndirizzo"}}}], "groupFormat": "Col TL 20 28 28 20", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"caption": {"columnImportance": "", "visible": false, "captionFor": "", "control": {"format": "Standard", "testID": "202305221213120415104"}, "value": "Gentile cliente, essendo residente all'estero le chiediamo cortesemente di rivolgersi all’agenzia. Grazie.", "testID": "202305221213120415104"}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": true, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "controlFormat": "Full Cancel button", "formatType": "text", "showReadOnlyValidation": "false"}], "actionSets": [], "type": "px<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "Ambito.Proprietario.Residenza.pyTemplateButton", "labelFormat": "TEXT APP WHB16U WEB WHB18 WHB18 WHB18", "disabled": false, "testID": "202303131227370797109", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateButton", "customAttributes": {"componentID": "cancelAddressButton"}, "showLabel": true}}, {"field": {"visible": false, "validateAs": "", "disabled": false, "fieldID": "pyTemplateButton", "customAttributes": {"submitButton": "formContainer", "GestioneProcesso.MostraBoxDomicilio": "true", "componentID": "confirmAddressButton", "GestioneProcesso.MostraBoxResidenza": "true", "GestioneProcesso.StepSuccessivo": "Conferma"}}}, {"field": {"visible": false, "validateAs": "", "disabled": false, "fieldID": "pyTemplateButton", "customAttributes": {"GestioneProcesso.MostraBoxIndirizzoImmobile": "true", "submitButton": "formContainer", "componentID": "confirmAddressButton", "GestioneProcesso.StepSuccessivo": "Conferma", "GestioneProcesso.MostraBoxIndirizzoResidenza": "true"}}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": true, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "controlFormat": "Full Cancel button", "formatType": "text", "showReadOnlyValidation": "false"}], "actionSets": [{"actions": [{"action": "runDataTransform"}, {"action": "finishAssignment"}], "events": [{"event": "click"}]}], "type": "px<PERSON><PERSON><PERSON>", "label": "Conferma"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "Ambito.Proprietario.Residenza.pyTemplateButton", "labelFormat": "TEXT APP WHB16U WEB WHB18 WHB18 WHB18", "disabled": false, "testID": "202303131227370797744", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateButton", "customAttributes": {"submitButton": "formContainer", "componentID": "confirmAddressButton", "GestioneProcesso.StepSuccessivo": "Conferma"}, "showLabel": true}}], "groupFormat": "Col TL 32 32 24 16", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}}], "groupFormat": "Col TL 32 32 24 16", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "", "control": {"format": "Text APP GDL16 WEB BDL16 BDL16 BDL16", "testID": "202302071700200852101"}, "value": "Non trovi il tuo indirizzo?", "testID": "202302071700200852101"}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": true, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "controlFormat": "SimpleTPD", "formatType": "text", "showReadOnlyValidation": "false"}], "actionSets": [{"actions": [{"action": "runDataTransform"}, {"action": "refresh", "refreshFor": "SW5kaXJpenpvUmVzaWRlbnphVUctSW5zLVBVLURhdGEtSW5kaXJpenpvLnB5VGVtcGxhdGVCdXR0b24yMDIzMDIwNzE3MDAyMDA4NTQ0NCNweEJ1dHRvbiMxMQ%3D%3D"}], "events": [{"event": "click"}]}], "type": "px<PERSON><PERSON><PERSON>", "label": "Inserisci il tuo indirizzo manualmente"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "Ambito.Proprietario.Residenza.pyTemplateButton", "labelFormat": "TEXT APP GDMN16 WEB BDBN18 BDBN18 BDBN16", "disabled": false, "testID": "20230207170020085444", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateButton", "customAttributes": {"componentID": "manualAddressButton", "GestioneProcesso.StepSuccessivo": "ModificaIndirizzo"}, "showLabel": true}}], "groupFormat": "Responsive 2col", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": false, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [], "groupFormat": "Col TL 4 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "FormContainer", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": false, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}}, {"view": {"viewID": "BoxIndirizzoDiResidenza", "visible": false, "name": "BoxIndirizzoDiResidenza"}}], "title": ""}}], "title": ""}}, {"view": {"viewID": "BoxIndirizzoDiResidenzaAssicurato", "visible": false, "name": "BoxIndirizzoDiResidenzaAssicurato"}}, {"view": {"reference": "Ambito", "validationMessages": "", "viewID": "UsoImmobile", "visible": true, "titleFormat": "", "name": "UsoImmobile", "appliesTo": "UG-Ins-PU-Data-Ambito", "groups": [{"view": {"reference": "Ambito.Bene", "validationMessages": "", "viewID": "UsoImmobile", "visible": true, "titleFormat": "", "name": "UsoImmobile", "appliesTo": "UG-Ins-PU-Data-Bene", "groups": [{"view": {"reference": "Ambito.Bene.Casa", "validationMessages": "", "viewID": "UsoImmobile", "visible": true, "titleFormat": "", "name": "UsoImmobile", "appliesTo": "UG-Ins-PU-Data-Casa", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "", "control": {"format": "Text APP BDM 16 WEB BDB20C BDB20C BDB16C", "testID": "202212271254490769430"}, "value": "È la casa dove vivi abitualmente?", "testID": "202212271254490769430"}}, {"caption": {"columnImportance": "", "visible": false, "captionFor": "", "control": {"format": "Text APP BDM 16 WEB BDB20C BDB20C BDB16C", "testID": "202212271254490769430"}, "value": "È la casa dove vive abitualmente?", "testID": "202212271254490769430"}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"dataPagePrompt": "label", "groupOrder": "asc", "orientation": "vertical", "listSource": "datapage", "textAlign": "Left", "wrapAfter": 3, "tooltip": "", "enableGrouping": false, "groupBy": "", "minChars": "", "dataPageParams": [{"valueReference": {"reference": "pyWorkPage.GestioneProcesso.IdTemplate", "lastSavedValue": "181cbdfd-e9e6-4def-b33e-3eba5afa5dee"}, "name": "id"}, {"name": "detailLevel"}, {"valueReference": {"reference": "pyWorkPage.Ambito.CodiceProdottoWPT", "lastSavedValue": "PUCASA"}, "name": "<PERSON><PERSON><PERSON>"}, {"valueReference": {"reference": "pyWorkPage.Ambito.Bene.DescrizioneBeneWPT", "lastSavedValue": ""}, "name": "<PERSON><PERSON>"}, {"valueReference": {"reference": "pyWorkPage.Ambito.Chiave", "lastSavedValue": "CASA APRILE 25"}, "name": "Chiave"}, {"valueReference": {"reference": ".UtenteProprietario", "lastSavedValue": "1"}, "name": "UtenteProprietario"}, {"valueReference": {"reference": ".UtenteAffittuario", "lastSavedValue": "1"}, "name": "UtenteAffittuario"}], "modeType": "editable", "dataPageValue": "Valore", "controlFormat": "", "dataPageTooltip": "", "loadMode": "auto", "options": [{"value": "Sì", "key": "1"}, {"value": "No, ci vivo saltuariamente", "key": "2"}, {"value": "No, la affitto a terzi", "key": "3"}], "dataPageID": "D_DominioTipoDimora_Editable", "specifySize": "auto", "formatType": "text", "obfuscated": false, "maxChars": ""}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false, "showReadOnlyValidation": "false"}], "actionSets": [{"actions": [{"action": "postValue"}], "events": [{"event": "change"}]}], "type": "pxRadioButtons"}, "label": "", "type": "Text", "required": true, "validateAs": "", "reference": "Ambito.Bene.Casa.TipoDimora", "labelFormat": "", "disabled": false, "testID": "20230102151230070383", "value": "1", "maxLength": 0, "expectedLength": "", "fieldID": "TipoDimora", "customAttributes": {"style": "FlatCardInverted"}}}], "groupFormat": "Col TL TC 32 16 16 16", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}}], "title": ""}}], "title": ""}}, {"view": {"reference": "Ambito", "validationMessages": "", "viewID": "PresenzaPertinenze", "visible": true, "titleFormat": "", "name": "PresenzaPertinenze", "appliesTo": "UG-Ins-PU-Data-Ambito", "groups": [{"view": {"reference": "Ambito.Bene", "validationMessages": "", "viewID": "PresenzaPertinenze", "visible": true, "titleFormat": "", "name": "PresenzaPertinenze", "appliesTo": "UG-Ins-PU-Data-Bene", "groups": [{"view": {"reference": "Ambito.Bene.Casa", "validationMessages": "", "viewID": "PresenzaPertinenze", "visible": true, "titleFormat": "", "name": "PresenzaPertinenze", "appliesTo": "UG-Ins-PU-Data-Casa", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "", "control": {"format": "Text APP BDM 16 WEB BDB20C BDB20C BDB16C", "testID": "202212271254490769430"}, "value": "Sono presenti pertinenze o dipendenze (es. box, garage, cantine, ecc.) al piano interrato o seminterrato?", "testID": "202212271254490769430"}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore", "iconSource": "image", "iconImage": "dsmimages/pzInformation.png"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "controlFormat": "Standard", "formatType": "text", "showReadOnlyValidation": "false", "iconSource": "image", "iconImage": "dsmimages/pzInformation.png"}], "actionSets": [{"actions": [{"action": "localAction", "actionProcess": {"localAction": "TooltipPertinenze", "localActionFormat": "", "customTemplate": "pzModalTemplate", "target": "overlay"}}], "events": [{"event": "click"}]}], "type": "pxIcon"}, "label": "Image", "type": "Text", "required": false, "validateAs": "", "reference": "Ambito.Bene.Casa.pyTemplateGeneric", "labelFormat": "Standard", "disabled": false, "testID": "202301021132400354827", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateGeneric", "customAttributes": {"tooltipDirection": "TooltipLU", "onlyIcon": "true", "size": "S", "resource": "info", "tooltipID": "tooltip1", "type": "icon"}, "showLabel": false}}], "groupFormat": "Row SBC 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"dataPagePrompt": "label", "groupOrder": "asc", "orientation": "horizontal", "listSource": "datapage", "textAlign": "Left", "wrapAfter": 3, "tooltip": "", "enableGrouping": false, "groupBy": "", "minChars": "", "dataPageParams": [{"valueReference": {"reference": "pyWorkPage.GestioneProcesso.IdTemplate", "lastSavedValue": "181cbdfd-e9e6-4def-b33e-3eba5afa5dee"}, "name": "id"}, {"name": "detailLevel"}, {"valueReference": {"reference": "pyWorkPage.Ambito.CodiceProdottoWPT", "lastSavedValue": "PUCASA"}, "name": "<PERSON><PERSON><PERSON>"}, {"valueReference": {"reference": "pyWorkPage.Ambito.Bene.DescrizioneBeneWPT", "lastSavedValue": ""}, "name": "<PERSON><PERSON>"}, {"valueReference": {"reference": "pyWorkPage.Ambito.Chiave", "lastSavedValue": "CASA APRILE 25"}, "name": "Chiave"}], "modeType": "editable", "dataPageValue": "Valore", "controlFormat": "", "dataPageTooltip": "", "loadMode": "auto", "options": [{"value": "No", "key": "0"}, {"value": "Sì", "key": "1"}], "dataPageID": "D_DominioPertinenze", "specifySize": "auto", "formatType": "text", "obfuscated": false, "maxChars": ""}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false, "showReadOnlyValidation": "false"}], "actionSets": [{"actions": [{"action": "postValue"}], "events": [{"event": "change"}]}], "type": "pxRadioButtons"}, "label": "", "type": "Text", "required": true, "validateAs": "", "reference": "Ambito.Bene.Casa.PresenzaPertinenze", "labelFormat": "", "disabled": false, "testID": "20230102151230070383", "value": "1", "maxLength": 0, "expectedLength": "", "fieldID": "PresenzaPertinenze", "customAttributes": {"style": "FlatCardInverted"}}}], "groupFormat": "Col TL TC 32 16 16 16", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}}], "title": ""}}], "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "controlFormat": "Full Positive Button", "formatType": "text", "showReadOnlyValidation": "false"}], "actionSets": [{"actions": [{"action": "finishAssignment"}], "events": [{"event": "click"}]}], "type": "px<PERSON><PERSON><PERSON>", "label": "Prosegui"}, "label": "<PERSON><PERSON>", "type": "Text", "required": false, "validateAs": "", "reference": "pyTemplateButton", "labelFormat": "Standard", "disabled": false, "testID": "202212271254490769947", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateButton", "customAttributes": {"loadingList": "", "submitButton": "true", "specialLoading": "false", "position": "bottom", "paddingApp": "0 48 0 0"}, "showLabel": false}}], "groupFormat": "CustomPosition", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "Col TL TC 32 32 32 32 MW496", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "P 0 D 0 0 0 16 T 0 0 0 16 M 0 0 0 16", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "StackedLayout", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "P 20 32 D 0 32 T 0 32 M 16 16", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}, "actionID": "DettagliResidenza", "caseID": "UG-INS-PU-WORK-QUOTAZIONE Q-46351", "name": "Dettagli sulla residenza"}, "metaBodyResponse": {"assignmentId": "ASSIGN-WORKLIST UG-INS-PU-WORK-QUOTAZIONE Q-46351!RACCOLTADATICASA", "actionId": "DettagliResidenza"}, "analyticsBodyResponse": {}}