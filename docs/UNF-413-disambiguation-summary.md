# UNF-413 Disambiguation Interface Requirements

This document summarizes the functional and technical requirements identified from
`UNF-413 - Processo Unica - Interfaccia di Disambiguazione Ambito Unica.pdf`.

## Functional Overview

The interface is organized in five phases:

1. **Base Components (LWC)**
   - `dc_selectorProtezione`: dynamic dropdown for protection scope retrieved via web service.
   - `dc_addressSearch`: address input with Google Autocomplete and manual entry component invoking address normalization.
   - `dc_datePicker`: date picker validating the effect date is today or later.
2. **Scope Specific Components (LWC)**
   - `dc_petForm`: animal type (dog/cat) and age with validation and dynamic domain via web service.
   - `dc_infortuniForm`: multi-step form collecting tax code and effect date (step 1) and profession, occupation, residence and family status (step 2) with profession→occupation dependency.
   - `dc_veicoliForm`: plate number, vehicle type, birth date, residence and merit class.
   - `dc_casaForm` / `dc_famigliaForm`: house type, solar panels (conditionally visible) and residence.
   - `dc_viaggiForm`: destination country, travel period and travellers count.
   - `dc_mobilitaForm` / `dc_saluteForm`: birth date, residence, profession, occupation and family status.
3. **Apex Service Layer**
   - `dc_DisambiguationService.cls`: provides `invokeInterpreter(payload)` to call `c-interprete` with mapping of protection scope and process data.
   - `dc_AddressService.cls`: exposes `normalizeAddress` to integrate the address normalization service.
   - `dc_MetadataService.cls`: standard caching for picklist values retrieved from web services.
4. **Dynamic Dispatcher**
   - `dc_formDispatcher`: renders the appropriate form according to the selected protection scope.
5. **Final Invocation**
   - `dc_stepFinal`: "Prosegui" button validates inputs and passes the payload to `<c-interprete>`.

## Technical Constraints

- All forms are implemented as Lightning Web Components.
- Web services provide dynamic values for protection scopes and other picklists.
- The dispatcher must load components lazily based on user selection.
- Address normalization and interpreter invocation are handled via Apex services.

