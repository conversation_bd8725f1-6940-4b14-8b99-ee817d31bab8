# AGENTS.md – Linee guida per contributori e sviluppo AI-assisted

Questo file definisce le linee guida per la gestione delle attività (`task`) e dei documenti Markdown nel repository `UNIPOL-REPO`, ottimizzate per l'integrazione con strumenti AI come OpenAI Codex, Copilot, Cursor IDE e agenti GPT-based.

---

## 📁 Struttura del repository

- `README.md`: panoramica e quick start del progetto
- `docs/`: documentazione tecnica dei moduli e logica di business
- `tasks/`: un file `.md` per ogni attività, organizzato in sottocartelle per ambito (es. `UNF-413`, `UNF-220`)
- `.github/copilot-instructions.md`: (opzionale) istruzioni Copilot globali
- `.cursor/index.mdc`: (opzionale) regole AI globali per Cursor IDE

---

## 📌 Stati delle task

Ogni file Markdown `.md` in `tasks/` rappresenta una task, con stato definito tramite tag:

- `[TODO]`: da iniziare
- `[INPROGRESS]`: in lavorazione
- `[READY-TEST]`: pronta per collaudo/test
- `[DONE]`: completata

---

## 🧾 Metadati YAML

Ogni file task deve iniziare con un blocco YAML strutturato:

```yaml
---
id: FE-B2C-42
UNF: UNF-413
title: Disambiguation Interface LWC + Apex
type: feature        # feature | bugfix | refactor | chore | spike
priority: high       # low | medium | high | critical
component: disambiguation-interface
status: TODO
depends_on: [DB-STRUCT-02]
blocked_by: []
owner: jean.sanchez
effort: 5             # story points
estimated_time: 4h
tags: [salesforce, lwc, apex, dynamic-ui]
---
````

---

## 🧱 Struttura del file task

````markdown
# Titolo della Task

## Descrizione
Breve spiegazione funzionale e tecnica del task da realizzare. Includere link ai requisiti se presenti.

## Motivazione
Perché è importante completare questa attività? Quale valore aggiunge?

## Criteri di Accettazione
- [ ] Il componente deve validare la data maggiore di oggi
- [ ] L’indirizzo deve essere normalizzato via WS
- [ ] L’invocazione a `c-interprete` deve seguire lo schema `<payload>`

## Structured Chain-of-Thought (SCoT)
1. Analizzare la struttura corrente
2. Definire i componenti LWC coinvolti
3. Generare mock dei payload WS
4. Sviluppare la UI con validazioni dinamiche
5. Collegare Apex layer e gestione eventi
6. Testare tutti i flussi end-to-end

## Prompt AI (facoltativo)

> ### Prompt: Generazione LWC dinamico
> **Ruolo:** Salesforce Architect  
> **Obiettivo:** Generare un LWC dinamico che renderizzi form specifici per ogni ambito (`PUPET`, `PUVEICOLO`, etc.), sulla base della selezione utente  
> **Input:** Lista di campi da mostrare per ambito (da metadati WS)  
> **Output atteso:** Componente LWC che renderizza dinamicamente i form con validazione e output serializzato in JSON  
> **Passaggi:**  
> 1. Analizza lista campi per ambito  
> 2. Genera struttura LWC dinamica  
> 3. Applica validazioni condizionali  
> 4. Crea evento finale `onFormSubmit(payload)`

---

## 💡 Best Practices

### Markdown
- Utilizzare intestazioni H2/H3 per separare sezioni
- Usare elenchi puntati per criteri di accettazione
- Non includere codice non eseguibile nello YAML iniziale

### AI Integration
- Scrivere task “prompt-friendly”: chiari, formattati, ragionati
- Usare `Structured Chain-of-Thought` per facilitare reasoning step-by-step
- Utilizzare prompt pre-strutturati (Role, Input, Output, Step)

### Copilot / Cursor
- Definire un file `.github/copilot-instructions.md` per comportamenti globali
- Inserire `.cursor/index.mdc` per regole condivise tra task e componenti

Esempio `copilot-instructions.md`:
```md
# Copilot Instructions
- Usa indentazione 2 spazi
- Nomina le variabili in camelCase
- Evita logica annidata complessa
````

---

## ✅ Git, Commit e PR

### Commit

* Usare [Conventional Commits](https://www.conventionalcommits.org):

  ```text
  feat(disambiguation): add dynamic LWC dispatcher component
  fix(viaggiForm): correct date validation logic
  ```

### Pull Request

Ogni PR deve includere:

* **Summary**: cosa è stato modificato
* **Testing**: evidenza test effettuati, es. output comandi, snapshot, etc.
* **Motivazione**: se la modifica è urgente, breaking o strategica
* **Checklist**:

  * [ ] Task completata e validata
  * [ ] PR conforme a `AGENTS.md`
  * [ ] PR linkata alla task YAML (`id:`)

---

## 📎 Esempi e Template

File esempio in `tasks/_template.md` per task future.

```shell
cp tasks/_template.md tasks/UNF-413/FE-B2C-42.md
```